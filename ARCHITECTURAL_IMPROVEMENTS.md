# Architectural Improvements for pbh-evo

## Current Status Assessment

### Completed (Phase 1)
- ✅ QNM calculation interface (C-Julia integration)
- ✅ Superradiance rate calculations  
- ✅ Basic data structures and interfaces
- ✅ Caching system for QNM results
- ✅ Test programs

### Critical Missing Components (Phase 2)
- ❌ Main evolution driver
- ❌ BlackHawk integration
- ❌ Adaptive time stepping
- ❌ Configuration management
- ❌ Error handling & logging
- ❌ Complete test suite

## Architectural Improvements

### 1. Core Architecture Restructuring

#### 1.1 Modular Design
```
src/
├── core/                    # Core evolution engine
│   ├── evolution.c         # Main evolution driver
│   ├── timestep.c          # Adaptive time stepping
│   ├── state.c             # Black hole state management
│   └── diagnostics.c       # Energy conservation, etc.
├── physics/                # Physics modules
│   ├── hawking/            # Hawking radiation interface
│   ├── superradiance/      # Superradiance calculations
│   └── qnm/                # QNM calculations
├── utils/                  # Utilities
│   ├── config.c            # Configuration management
│   ├── logging.c           # Logging system
│   ├── io.c                # Input/output handling
│   └── cache.c             # Improved caching
└── tests/                  # Comprehensive test suite
```

#### 1.2 Improved Data Structures
```c
// Unified black hole state
typedef struct {
    double M;                    // Mass
    double a;                    // Dimensionless spin
    double Q;                    // Charge (optional)
    double t;                    // Current time
    double dt;                   // Current timestep
    
    // Scalar field cloud properties
    double mu_scalar;            // Scalar field mass
    double cloud_mass;           // Current cloud mass
    double cloud_energy;         // Cloud energy
    
    // Evolution rates
    double dM_dt_hawking;        // Hawking mass rate
    double da_dt_hawking;        // Hawking spin rate
    double dM_dt_super;          // Superradiance mass rate
    double da_dt_super;          // Superradiance spin rate
    
    // Diagnostics
    double total_energy_radiated;
    double total_angular_momentum_lost;
    int evolution_step;
} BlackHoleState;

// Configuration structure
typedef struct {
    // Evolution parameters
    double t_initial;
    double t_final;
    double dt_initial;
    double dt_min;
    double dt_max;
    double tolerance;
    
    // Physics parameters
    int enable_hawking;
    int enable_superradiance;
    double scalar_mass;
    int l_max;
    int m_max;
    int n_max;
    double initial_cloud_fraction;
    
    // Numerical parameters
    int adaptive_timestep;
    int max_iterations;
    double convergence_threshold;
    
    // Output parameters
    char output_dir[256];
    int output_frequency;
    int verbose;
} EvolutionConfig;
```

### 2. Performance Optimizations

#### 2.1 Improved Julia-C Interface
- Replace system calls with Julia C API
- Implement persistent Julia session
- Pre-compile QNM calculation functions
- Use shared memory for data exchange

#### 2.2 Enhanced Caching System
- Hash-based cache lookup
- LRU cache eviction
- Persistent cache to disk
- Interpolation for nearby parameters

#### 2.3 Parallel Computing
- OpenMP for mode summation
- MPI for parameter sweeps
- GPU acceleration for QNM calculations (future)

### 3. Robustness Improvements

#### 3.1 Error Handling
```c
typedef enum {
    EVOLUTION_SUCCESS = 0,
    EVOLUTION_ERROR_CONVERGENCE,
    EVOLUTION_ERROR_TIMESTEP,
    EVOLUTION_ERROR_QNM,
    EVOLUTION_ERROR_IO,
    EVOLUTION_ERROR_CONFIG
} EvolutionError;
```

#### 3.2 Logging System
- Configurable log levels
- Structured logging with timestamps
- Performance profiling
- Debug output for troubleshooting

#### 3.3 Input Validation
- Parameter range checking
- Physical consistency checks
- Unit conversion validation

### 4. Integration Strategy

#### 4.1 BlackHawk Integration
- Wrapper functions for BlackHawk routines
- Unified parameter interface
- Consistent unit system
- Error propagation

#### 4.2 Adaptive Time Stepping
```c
double calculate_adaptive_timestep(BlackHoleState *bh, EvolutionConfig *config) {
    double dt_hawking = estimate_hawking_timescale(bh);
    double dt_super = estimate_superradiance_timescale(bh);
    double dt_stability = estimate_numerical_stability_limit(bh);
    
    double dt_new = fmin(fmin(dt_hawking, dt_super), dt_stability) * 0.1;
    
    // Apply constraints
    dt_new = fmax(dt_new, config->dt_min);
    dt_new = fmin(dt_new, config->dt_max);
    
    // Smooth timestep changes
    if (dt_new > 2.0 * bh->dt) dt_new = 2.0 * bh->dt;
    if (dt_new < 0.5 * bh->dt) dt_new = 0.5 * bh->dt;
    
    return dt_new;
}
```

### 5. Testing Framework

#### 5.1 Unit Tests
- QNM calculation accuracy
- Superradiance rate validation
- Energy conservation checks
- Numerical stability tests

#### 5.2 Integration Tests
- Full evolution scenarios
- Comparison with analytical limits
- Performance benchmarks
- Regression tests

#### 5.3 Validation Tests
- Literature comparison
- Known analytical solutions
- Extreme parameter regimes

### 6. Documentation and Usability

#### 6.1 API Documentation
- Doxygen-style comments
- Usage examples
- Parameter descriptions
- Physics background

#### 6.2 User Interface
- Command-line interface
- Configuration file format
- Progress reporting
- Result visualization tools

## Implementation Priority

### Phase 2A (Critical - 2 weeks)
1. Create main evolution driver
2. Implement BlackHawk integration
3. Add configuration management
4. Basic error handling

### Phase 2B (Important - 2 weeks)  
5. Adaptive time stepping
6. Improved Julia interface
7. Enhanced caching
8. Logging system

### Phase 2C (Enhancement - 2 weeks)
9. Comprehensive testing
10. Performance optimization
11. Documentation
12. Validation studies

## Success Metrics

1. **Functionality**: Complete evolution runs without crashes
2. **Accuracy**: Energy conservation < 1% error
3. **Performance**: 10x speedup from caching and optimization
4. **Robustness**: Handles edge cases gracefully
5. **Usability**: Clear documentation and examples
