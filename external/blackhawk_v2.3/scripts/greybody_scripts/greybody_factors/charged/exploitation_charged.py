##
# This script computes the high and low fitting parameters for charged BHs. The "blah" mentions should be replaced with the correct path to your tables/figures.

import numpy as np
import scipy.interpolate as scp
import scipy.integrate as sci
import pylab
from scipy.special import gamma
from scipy import stats

## Exploiting charged BHs results
# greybody factors for charged BHs
data_0 = np.genfromtxt("blah")
data_1 = np.genfromtxt("blah")
data_2 = np.genfromtxt("blah")
data_12 = np.genfromtxt("blah")

# comparative greybody factors for Schwarzschild BHs
data_0_comp = np.genfromtxt("blah")
data_1_comp = np.genfromtxt("blah")
data_2_comp = np.genfromtxt("blah")
data_12_comp = np.genfromtxt("blah")


# data at low energy, you can produce those by modifying the energy bounds in the Mathematica scripts
data_0_low = np.genfromtxt("blah")
data_1_low = np.genfromtxt("blah")
data_2_low = np.genfromtxt("blah")
data_12_low = np.genfromtxt("blah")

data_0_comp_low = np.genfromtxt("blah")
data_1_comp_low = np.genfromtxt("blah")
data_2_comp_low = np.genfromtxt("blah")
data_12_comp_low = np.genfromtxt("blah")

# be careful to reproduce the energy range you have used for the computations
enumber = len(data_0[0])
energies = np.zeros(enumber)
Emin = 0.01
Einter = 1.
Emax = 5.
for i in range(len(energies)):
    if i<100:
        energies[i] = 10**(np.log10(Emin) + (np.log10(Einter) - np.log10(Emin))/(100.)*i)
    else:
        energies[i] = Einter + (Emax - Einter)/99.*(i-100.)

energies_low = np.zeros(50)
for i in range(50):
    energies_low[i] = 10**(-6 + (-4 - (-6))/(50 - 1)*i)

index_min = 25
index_max = 150

M = 0.5

nbrQ = len(data_0)
rQ = np.zeros(nbrQ)
rQmin = 0.01
rQmax = 0.999
for i in range(nbrQ):
    rQ[i] = (1.-10**(np.log10(1-rQmin) + (np.log10(1-rQmax)-np.log10(1-rQmin))/(nbrQ-1)*i))*M

def rplus(M,rQ):
    return M*(1+np.sqrt(1 - 4*rQ**2/4/M**2))

def rminus(M,rQ):
    return M*(1-np.sqrt(1 - 4*rQ**2/4/M**2))

def T(M,rQ):
    return (rplus(M,rQ) - rminus(M,rQ))/(4*np.pi*rplus(M,rQ)**2)

## spin 0
fig_width_pt = 80*2.83465  # Get this from LaTeX using \showthe\columnwidth
inches_per_pt = 1.0/72.27               # Convert pt to inch
fig_width = fig_width_pt*inches_per_pt  # width in inches
fig_height_pt = 80*2.83465
fig_height = fig_height_pt*inches_per_pt     # height in inches
fig_size =  [fig_width,fig_height]
params = {'backend': 'ps',
          'axes.labelsize': 11,
          'axes.linewidth': 0.5,
          #'text.fontsize': 11,
          'figure.titlesize': 11,
          'legend.fontsize': 11,
          'xtick.labelsize': 11,
          'ytick.labelsize': 11,
          'text.usetex': True,
          'figure.figsize': fig_size}
pylab.rcParams.update(params)

f = pylab.figure(0)
f.clf()
ax = f.add_subplot(111)
ax.set_xscale("linear")
ax.set_yscale("linear")
ax.set_xlim(-6,-4)
#ax.set_ylim(-,0)
ax.set_xlabel("$\log_{10}(x)$")
ax.set_ylabel("$\log_{10}(Q_0)$")
ax.scatter(np.log10(energies_low),np.log10(data_0_comp_low*(np.exp(energies_low/T(M,0))-1.)),s = 1,color = "black",label = "$Q = 0\,\,{\\rm (Schwarzschild)}$")
ax.scatter(np.log10(energies_low),np.log10(data_0_low[0,:]*(np.exp(energies_low/T(M,rQ[0]))-1.)),s = 1,color = "blue",label = "$Q = %.3f\\times M$"%(rQ[0]/M))
ax.scatter(np.log10(energies_low),np.log10(data_0_low[10,:]*(np.exp(energies_low/T(M,rQ[10]))-1.)),s = 1,color = "green",label = "$Q = %.3f\\times M$"%(rQ[10]/M))
ax.scatter(np.log10(energies_low),np.log10(data_0_low[49,:]*(np.exp(energies_low/T(M,rQ[49]))-1.)),s = 1,color = "red",label = "$Q = %.3f\\times M$"%(rQ[49]/M))

slope, intercept, r_value, p_value, stderr = stats.linregress(np.log10(energies_low),np.log10(data_0_comp_low*(np.exp(energies_low/T(M,0))-1.)))

slopes = np.zeros(50)
intercepts = np.zeros(50)

for i in range(0,50):
    result = stats.linregress(np.log10(energies_low),np.log10(data_0_low[i,:]*(np.exp(energies_low/T(M,rQ[i]))-1.)))
    slopes[i] = result.slope
    intercepts[i] = result.intercept

ax.plot(np.log10(energies_low),slope*np.log10(energies_low)+intercept,linewidth = 2,color = "black",linestyle = "--")
ax.plot(np.log10(energies_low),slopes[0]*np.log10(energies_low)+intercepts[0],linewidth = 0.5,color = "blue")
ax.plot(np.log10(energies_low),slopes[10]*np.log10(energies_low)+intercepts[10],linewidth = 0.5,color = "green")
ax.plot(np.log10(energies_low),slopes[49]*np.log10(energies_low)+intercepts[49],linewidth = 0.5,color = "red")

ax.legend(loc = "best")
f.show()

f = pylab.figure(1)
f.clf()
ax = f.add_subplot(111)
ax.set_xscale("linear")
ax.set_yscale("linear")
ax.set_xlabel("$x$")
ax.set_ylabel("$Q_0$")
#ax.set_xlim(0,5)
#ax.set_ylim(0,1.5)
ax.scatter(energies[index_max:],data_0_comp[index_max:]*(np.exp(energies[index_max:]/T(M,0))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "black",label = "$Q = 0\,\,{\\rm (Schwarzschild)}$")
ax.scatter(energies[index_max:],data_0[0,index_max:]*(np.exp(energies[index_max:]/T(M,rQ[0]))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "blue",label = "$Q = %.3f\\times M$"%(rQ[0]/M))
ax.scatter(energies[index_max:],data_0[10,index_max:]*(np.exp(energies[index_max:]/T(M,rQ[10]))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "green",label = "$Q = %.3f\\times M$"%(rQ[10]/M))
ax.scatter(energies[index_max:],data_0[49,index_max:]*(np.exp(energies[index_max:]/T(M,rQ[49]))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "red",label = "$Q = %.3f\\times M$"%(rQ[49]/M))


limit = 0.
for i in range(index_max,len(data_0[0])):
    limit += data_0_comp[i]*(np.exp(energies[i]/T(M,0))-1.)/(27./4.*energies[i]**2)
limit = limit/(len(data_0[0])-index_max)

limits = np.zeros(50)
for j in range(50):
    for i in range(index_max,len(data_0[0])):
        limits[j] += data_0[j,i]*(np.exp(energies[i]/T(M,rQ[j]))-1.)/(27./4.*energies[i]**2)
    limits[j] = limits[j]/(len(data_0[0])-index_max)

ax.axhline(y = limit,xmin = 0,xmax = 1,linewidth = 0.5,color = "black")
ax.axhline(y = limits[0],xmin = 0,xmax = 1,linewidth = 0.5,color = "blue")
ax.axhline(y = limits[10],xmin = 0,xmax = 1,linewidth = 0.5,color = "green")
ax.axhline(y = limits[49],xmin = 0,xmax = 1,linewidth = 0.5,color = "red")

ax.legend(loc = "best")
f.show()

spin_0_path = "blah"

print("%15s%15s%15s%15s\n"%("Q/fits","a1","b1","limit"),file=open(spin_0_path,"w"),end = "")
print("%15.5e%15.5e%15.5e%15.5e\n"%(0.,slope,intercept,limit),file=open(spin_0_path,"a"),end = "")
for i in range(50):
    print("%15.5e%15.5e%15.5e%15.5e\n"%(rQ[i]/M,slopes[i],intercepts[i],limits[i]),file=open(spin_0_path,"a"),end = "")

## spin 1
fig_width_pt = 80*2.83465  # Get this from LaTeX using \showthe\columnwidth
inches_per_pt = 1.0/72.27               # Convert pt to inch
fig_width = fig_width_pt*inches_per_pt  # width in inches
fig_height_pt = 80*2.83465
fig_height = fig_height_pt*inches_per_pt     # height in inches
fig_size =  [fig_width,fig_height]
params = {'backend': 'ps',
          'axes.labelsize': 11,
          'axes.linewidth': 0.5,
          #'text.fontsize': 11,
          'figure.titlesize': 11,
          'legend.fontsize': 11,
          'xtick.labelsize': 11,
          'ytick.labelsize': 11,
          'text.usetex': True,
          'figure.figsize': fig_size}
pylab.rcParams.update(params)

f = pylab.figure(0)
f.clf()
ax = f.add_subplot(111)
ax.set_xscale("linear")
ax.set_yscale("linear")
ax.set_xlim(-6,-4)
#ax.set_ylim(-,0)
ax.set_xlabel("$\log_{10}(x)$")
ax.set_ylabel("$\log_{10}(Q_1)$")
ax.scatter(np.log10(energies_low),np.log10(data_1_comp_low*(np.exp(energies_low/T(M,0))-1.)),s = 1,color = "black",label = "$Q = 0\,\,{\\rm (Schwarzschild)}$")
ax.scatter(np.log10(energies_low),np.log10(data_1_low[0,:]*(np.exp(energies_low/T(M,rQ[0]))-1.)),s = 1,color = "blue",label = "$Q = %.3f\\times M$"%(rQ[0]/M))
ax.scatter(np.log10(energies_low),np.log10(data_1_low[10,:]*(np.exp(energies_low/T(M,rQ[10]))-1.)),s = 1,color = "green",label = "$Q = %.3f\\times M$"%(rQ[10]/M))
ax.scatter(np.log10(energies_low),np.log10(data_1_low[49,:]*(np.exp(energies_low/T(M,rQ[49]))-1.)),s = 1,color = "red",label = "$Q = %.3f\\times M$"%(rQ[49]/M))

slope, intercept, r_value, p_value, stderr = stats.linregress(np.log10(energies_low),np.log10(data_1_comp_low*(np.exp(energies_low/T(M,0))-1.)))

slopes = np.zeros(50)
intercepts = np.zeros(50)

for i in range(0,50):
    result = stats.linregress(np.log10(energies_low),np.log10(data_1_low[i,:]*(np.exp(energies_low/T(M,rQ[i]))-1.)))
    slopes[i] = result.slope
    intercepts[i] = result.intercept

ax.plot(np.log10(energies_low),slope*np.log10(energies_low)+intercept,linewidth = 2,color = "black")
ax.plot(np.log10(energies_low),slopes[0]*np.log10(energies_low)+intercepts[0],linewidth = 0.5,color = "blue")
ax.plot(np.log10(energies_low),slopes[10]*np.log10(energies_low)+intercepts[10],linewidth = 0.5,color = "green")
ax.plot(np.log10(energies_low),slopes[49]*np.log10(energies_low)+intercepts[49],linewidth = 0.5,color = "red")

ax.legend(loc = "best")
f.show()

f = pylab.figure(1)
f.clf()
ax = f.add_subplot(111)
ax.set_xscale("linear")
ax.set_yscale("linear")
ax.set_xlabel("$x$")
ax.set_ylabel("$Q_1$")
#ax.set_xlim(0,5)
#ax.set_ylim(0,1.5)
ax.scatter(energies[index_max:],data_1_comp[index_max:]*(np.exp(energies[index_max:]/T(M,0))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "black",label = "$Q = 0\,\,{\\rm (Schwarzschild)}$")
ax.scatter(energies[index_max:],data_1[0,index_max:]*(np.exp(energies[index_max:]/T(M,rQ[0]))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "blue",label = "$Q = %.3f\\times M$"%(rQ[0]/M))
ax.scatter(energies[index_max:],data_1[10,index_max:]*(np.exp(energies[index_max:]/T(M,rQ[10]))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "green",label = "$Q = %.3f\\times M$"%(rQ[10]/M))
ax.scatter(energies[index_max:],data_1[49,index_max:]*(np.exp(energies[index_max:]/T(M,rQ[49]))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "red",label = "$Q = %.3f\\times M$"%(rQ[49]/M))


limit = 0.
for i in range(index_max,len(data_1[0])):
    limit += data_1_comp[i]*(np.exp(energies[i]/T(M,0))-1.)/(27./4.*energies[i]**2)
limit = limit/(len(data_1[0])-index_max)

limits = np.zeros(50)
for j in range(50):
    for i in range(index_max,len(data_1[0])):
        limits[j] += data_1[j,i]*(np.exp(energies[i]/T(M,rQ[j]))-1.)/(27./4.*energies[i]**2)
    limits[j] = limits[j]/(len(data_1[0])-index_max)

ax.axhline(y = limit,xmin = 0,xmax = 1,linewidth = 0.5,color = "black")
ax.axhline(y = limits[0],xmin = 0,xmax = 1,linewidth = 0.5,color = "blue")
ax.axhline(y = limits[10],xmin = 0,xmax = 1,linewidth = 0.5,color = "green")
ax.axhline(y = limits[49],xmin = 0,xmax = 1,linewidth = 0.5,color = "red")

ax.legend(loc = "best")
f.show()

spin_1_path = "blah"

print("%15s%15s%15s%15s\n"%("Q/fits","a1","b1","limit"),file=open(spin_1_path,"w"),end = "")
print("%15.5e%15.5e%15.5e%15.5e\n"%(0.,slope,intercept,limit),file=open(spin_1_path,"a"),end = "")
for i in range(50):
    print("%15.5e%15.5e%15.5e%15.5e\n"%(rQ[i]/M,slopes[i],intercepts[i],limits[i]),file=open(spin_1_path,"a"),end = "")

## spin 2
fig_width_pt = 80*2.83465  # Get this from LaTeX using \showthe\columnwidth
inches_per_pt = 1.0/72.27               # Convert pt to inch
fig_width = fig_width_pt*inches_per_pt  # width in inches
fig_height_pt = 80*2.83465
fig_height = fig_height_pt*inches_per_pt     # height in inches
fig_size =  [fig_width,fig_height]
params = {'backend': 'ps',
          'axes.labelsize': 11,
          'axes.linewidth': 0.5,
          #'text.fontsize': 11,
          'figure.titlesize': 11,
          'legend.fontsize': 11,
          'xtick.labelsize': 11,
          'ytick.labelsize': 11,
          'text.usetex': True,
          'figure.figsize': fig_size}
pylab.rcParams.update(params)

f = pylab.figure(0)
f.clf()
ax = f.add_subplot(111)
ax.set_xscale("linear")
ax.set_yscale("linear")
ax.set_xlim(-6,-4)
#ax.set_ylim(-,0)
ax.set_xlabel("$\log_{10}(x)$")
ax.set_ylabel("$\log_{10}(Q_2)$")
ax.scatter(np.log10(energies_low),np.log10(data_2_comp_low*(np.exp(energies_low/T(M,0))-1.)),s = 1,color = "black",label = "$Q = 0\,\,{\\rm (Schwarzschild)}$")
ax.scatter(np.log10(energies_low),np.log10(data_2_low[0,:]*(np.exp(energies_low/T(M,rQ[0]))-1.)),s = 1,color = "blue",label = "$Q = %.3f\\times M$"%(rQ[0]/M))
ax.scatter(np.log10(energies_low),np.log10(data_2_low[10,:]*(np.exp(energies_low/T(M,rQ[10]))-1.)),s = 1,color = "green",label = "$Q = %.3f\\times M$"%(rQ[10]/M))
ax.scatter(np.log10(energies_low),np.log10(data_2_low[49,:]*(np.exp(energies_low/T(M,rQ[49]))-1.)),s = 1,color = "red",label = "$Q = %.3f\\times M$"%(rQ[49]/M))

slope, intercept, r_value, p_value, stderr = stats.linregress(np.log10(energies_low),np.log10(data_2_comp_low*(np.exp(energies_low/T(M,0))-1.)))

slopes = np.zeros(50)
intercepts = np.zeros(50)

for i in range(0,50):
    result = stats.linregress(np.log10(energies_low),np.log10(data_2_low[i,:]*(np.exp(energies_low/T(M,rQ[i]))-1.)))
    slopes[i] = result.slope
    intercepts[i] = result.intercept

ax.plot(np.log10(energies_low),slope*np.log10(energies_low)+intercept,linewidth = 2,color = "black")
ax.plot(np.log10(energies_low),slopes[0]*np.log10(energies_low)+intercepts[0],linewidth = 0.5,color = "blue")
ax.plot(np.log10(energies_low),slopes[10]*np.log10(energies_low)+intercepts[10],linewidth = 0.5,color = "green")
ax.plot(np.log10(energies_low),slopes[49]*np.log10(energies_low)+intercepts[49],linewidth = 0.5,color = "red")

ax.legend(loc = "best")
f.show()

f = pylab.figure(1)
f.clf()
ax = f.add_subplot(111)
ax.set_xscale("linear")
ax.set_yscale("linear")
ax.set_xlabel("$x$")
ax.set_ylabel("$Q_2$")
#ax.set_xlim(0,5)
#ax.set_ylim(0,1.5)
ax.scatter(energies[index_max:],data_2_comp[index_max:]*(np.exp(energies[index_max:]/T(M,0))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "black",label = "$Q = 0\,\,{\\rm (Schwarzschild)}$")
ax.scatter(energies[index_max:],data_2[0,index_max:]*(np.exp(energies[index_max:]/T(M,rQ[0]))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "blue",label = "$Q = %.3f\\times M$"%(rQ[0]/M))
ax.scatter(energies[index_max:],data_2[10,index_max:]*(np.exp(energies[index_max:]/T(M,rQ[10]))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "green",label = "$Q = %.3f\\times M$"%(rQ[10]/M))
ax.scatter(energies[index_max:],data_2[49,index_max:]*(np.exp(energies[index_max:]/T(M,rQ[49]))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "red",label = "$Q = %.3f\\times M$"%(rQ[49]/M))

limit = 0.
for i in range(index_max,len(data_2[0])):
    limit += data_2_comp[i]*(np.exp(energies[i]/T(M,0))-1.)/(27./4.*energies[i]**2)
limit = limit/(len(data_2[0])-index_max)

limits = np.zeros(50)
for j in range(50):
    for i in range(index_max,len(data_2[0])):
        limits[j] += data_2[j,i]*(np.exp(energies[i]/T(M,rQ[j]))-1.)/(27./4.*energies[i]**2)
    limits[j] = limits[j]/(len(data_2[0])-index_max)

ax.axhline(y = limit,xmin = 0,xmax = 1,linewidth = 0.5,color = "black")
ax.axhline(y = limits[0],xmin = 0,xmax = 1,linewidth = 0.5,color = "blue")
ax.axhline(y = limits[10],xmin = 0,xmax = 1,linewidth = 0.5,color = "green")
ax.axhline(y = limits[49],xmin = 0,xmax = 1,linewidth = 0.5,color = "red")

ax.legend(loc = "best")
f.show()

spin_2_path = "blah"

print("%15s%15s%15s%15s\n"%("Q/fits","a1","b1","limit"),file=open(spin_2_path,"w"),end = "")
print("%15.5e%15.5e%15.5e%15.5e\n"%(0.,slope,intercept,limit),file=open(spin_2_path,"a"),end = "")
for i in range(50):
    print("%15.5e%15.5e%15.5e%15.5e\n"%(rQ[i]/M,slopes[i],intercepts[i],limits[i]),file=open(spin_2_path,"a"),end = "")

## spin 1/2
fig_width_pt = 80*2.83465  # Get this from LaTeX using \showthe\columnwidth
inches_per_pt = 1.0/72.27               # Convert pt to inch
fig_width = fig_width_pt*inches_per_pt  # width in inches
fig_height_pt = 80*2.83465
fig_height = fig_height_pt*inches_per_pt     # height in inches
fig_size =  [fig_width,fig_height]
params = {'backend': 'ps',
          'axes.labelsize': 11,
          'axes.linewidth': 0.5,
          #'text.fontsize': 11,
          'figure.titlesize': 11,
          'legend.fontsize': 11,
          'xtick.labelsize': 11,
          'ytick.labelsize': 11,
          'text.usetex': True,
          'figure.figsize': fig_size}
pylab.rcParams.update(params)

f = pylab.figure(0)
f.clf()
ax = f.add_subplot(111)
ax.set_xscale("linear")
ax.set_yscale("linear")
ax.set_xlim(-6,-4)
#ax.set_ylim(-,0)
ax.set_xlabel("$\log_{10}(x)$")
ax.set_ylabel("$\log_{10}(Q_{1/2})$")
ax.scatter(np.log10(energies_low),np.log10(data_12_comp_low*(np.exp(energies_low/T(M,0))+1.)),s = 1,color = "black",label = "$Q = 0\,\,{\\rm (Schwarzschild)}$")
ax.scatter(np.log10(energies_low),np.log10(data_12_low[0,:]*(np.exp(energies_low/T(M,rQ[0]))+1.)),s = 1,color = "blue",label = "$Q = %.3f\\times M$"%(rQ[0]/M))
ax.scatter(np.log10(energies_low),np.log10(data_12_low[10,:]*(np.exp(energies_low/T(M,rQ[10]))+1.)),s = 1,color = "green",label = "$Q = %.3f\\times M$"%(rQ[10]/M))
ax.scatter(np.log10(energies_low),np.log10(data_12_low[49,:]*(np.exp(energies_low/T(M,rQ[49]))+1.)),s = 1,color = "red",label = "$Q = %.3f\\times M$"%(rQ[49]/M))

slope, intercept, r_value, p_value, stderr = stats.linregress(np.log10(energies_low),np.log10(data_12_comp_low*(np.exp(energies_low/T(M,0))+1.)))

slopes = np.zeros(50)
intercepts = np.zeros(50)

for i in range(0,50):
    result = stats.linregress(np.log10(energies_low),np.log10(data_12_low[i,:]*(np.exp(energies_low/T(M,rQ[i]))+1.)))
    slopes[i] = result.slope
    intercepts[i] = result.intercept

ax.plot(np.log10(energies_low),slope*np.log10(energies_low)+intercept,linewidth = 0.5,color = "black")
ax.plot(np.log10(energies_low),slopes[0]*np.log10(energies_low)+intercepts[0],linewidth = 0.5,color = "blue")
ax.plot(np.log10(energies_low),slopes[10]*np.log10(energies_low)+intercepts[10],linewidth = 0.5,color = "green")
ax.plot(np.log10(energies_low),slopes[49]*np.log10(energies_low)+intercepts[49],linewidth = 0.5,color = "red")

ax.legend(loc = "best")
f.show()

f = pylab.figure(1)
f.clf()
ax = f.add_subplot(111)
ax.set_xscale("linear")
ax.set_yscale("linear")
ax.set_xlabel("$x$")
ax.set_ylabel("$Q_{1/2}$")
#ax.set_xlim(0,5)
#ax.set_ylim(0,1.5)
ax.scatter(energies[index_max:],data_12_comp[index_max:]*(np.exp(energies[index_max:]/T(M,0))+1.)/(27./4.*energies[index_max:]**2),s = 1,color = "black",label = "$Q = 0\,\,{\\rm (Schwarzschild)}$")
ax.scatter(energies[index_max:],data_12[0,index_max:]*(np.exp(energies[index_max:]/T(M,rQ[0]))+1.)/(27./4.*energies[index_max:]**2),s = 1,color = "blue",label = "$Q = %.3f\\times M$"%(rQ[0]/M))
ax.scatter(energies[index_max:],data_12[10,index_max:]*(np.exp(energies[index_max:]/T(M,rQ[10]))+1.)/(27./4.*energies[index_max:]**2),s = 1,color = "green",label = "$Q = %.3f\\times M$"%(rQ[10]/M))
ax.scatter(energies[index_max:],data_12[49,index_max:]*(np.exp(energies[index_max:]/T(M,rQ[49]))+1.)/(27./4.*energies[index_max:]**2),s = 1,color = "red",label = "$Q = %.3f\\times M$"%(rQ[49]/M))

limit = 0.
for i in range(index_max,len(data_12[0])):
    limit += data_12_comp[i]*(np.exp(energies[i]/T(M,0))+1.)/(27./4.*energies[i]**2)
limit = limit/(len(data_12[0])-index_max)

limits = np.zeros(50)
for j in range(50):
    for i in range(index_max,len(data_12[0])):
        limits[j] += data_12[j,i]*(np.exp(energies[i]/T(M,rQ[j]))+1.)/(27./4.*energies[i]**2)
    limits[j] = limits[j]/(len(data_12[0])-index_max)

ax.axhline(y = limit,xmin = 0,xmax = 1,linewidth = 0.5,color = "black")
ax.axhline(y = limits[0],xmin = 0,xmax = 1,linewidth = 0.5,color = "blue")
ax.axhline(y = limits[10],xmin = 0,xmax = 1,linewidth = 0.5,color = "green")
ax.axhline(y = limits[49],xmin = 0,xmax = 1,linewidth = 0.5,color = "red")

ax.legend(loc = "best")
f.show()

spin_12_path = "blah"

print("%15s%15s%15s%15s\n"%("Q/fits","a1","b1","limit"),file=open(spin_12_path,"w"),end = "")
print("%15.5e%15.5e%15.5e%15.5e\n"%(0.,slope,intercept,limit),file=open(spin_12_path,"a"),end = "")
for i in range(50):
    print("%15.5e%15.5e%15.5e%15.5e\n"%(rQ[i]/M,slopes[i],intercepts[i],limits[i]),file=open(spin_12_path,"a"),end = "")

## Comparison to theory for all spins, high energy
fig_width_pt = 80*2.83465  # Get this from LaTeX using \showthe\columnwidth
inches_per_pt = 1.0/72.27               # Convert pt to inch
fig_width = fig_width_pt*inches_per_pt  # width in inches
fig_height_pt = 80*2.83465
fig_height = fig_height_pt*inches_per_pt     # height in inches
fig_size =  [fig_width,fig_height]
params = {'backend': 'ps',
          'axes.labelsize': 10,
          'axes.linewidth': 0.5,
          #'text.fontsize': 11,
          'figure.titlesize': 10,
          'legend.fontsize': 10,
          'xtick.labelsize': 10,
          'ytick.labelsize': 10,
          'text.usetex': True,
          'figure.figsize': fig_size}
pylab.rcParams.update(params)

f = pylab.figure(2)
f.clf()
ax = f.add_subplot(111)

fits0 = np.genfromtxt("blah",skip_header = 1)
fits1 = np.genfromtxt("blah",skip_header = 1)
fits2 = np.genfromtxt("blah",skip_header = 1)
fits12 = np.genfromtxt("blah",skip_header = 1)

nbrQQ = 500
rQQ = np.zeros(nbrQQ)
rQQmin = 0.0
rQQmax = 0.999
for i in range(nbrQQ):
    rQQ[i] = (1.-10**(np.log10(1-rQQmin) + (np.log10(1-rQQmax)-np.log10(1-rQQmin))/(nbrQQ-1)*i))*M
ratio = np.zeros(len(rQQ))
for i in range(len(rQQ)):
    ratio[i] = np.pi/8.*(3.*M+np.sqrt(9.*M**2-8.*(rQQ[i])**2))**4/(3.*M**2-2.*(rQQ[i])**2 + M*np.sqrt(9.*M**2 - 8.*(rQQ[i])**2))/27.*4./np.pi

ax.set_xscale("log")
ax.set_xlabel("$1-Q/M$")
ax.set_ylabel("$\\beta_{\infty}^{Q}$")
ax.set_xlim(1e-3,1)
ax.scatter(1-fits0[::2,0],fits0[::2,3],s = 10,marker = "+",color = "black",label = "${\\rm spin\,\,0}$")
ax.scatter(1-fits1[::2,0],fits1[::2,3],s = 10,marker = "x",color = "black",label = "${\\rm spin\,\,1}$")
ax.scatter(1-fits2[::2,0],fits2[::2,3],s = 10,marker = "o",color = "black",label = "${\\rm spin\,\,2}$")
ax.scatter(1-fits12[::2,0],fits12[::2,3],s = 10,marker = "s",color = "black",label = "${\\rm spin\,\,1/2}$")
ax.plot(1-rQQ*2.,ratio,color = 'red',linewidth = 1,label = "${\\rm theory}$")
ax.legend(loc = "best")

f.tight_layout(rect = [0,0,1,1])
f.show()
f.savefig("blah")

## Comparison between all spins for low energy
fig_width_pt = 85*2.83465  # Get this from LaTeX using \showthe\columnwidth
inches_per_pt = 1.0/72.27               # Convert pt to inch
fig_width = fig_width_pt*inches_per_pt  # width in inches
fig_height_pt = 80*2.83465
fig_height = fig_height_pt*inches_per_pt     # height in inches
fig_size =  [fig_width,fig_height]
params = {'backend': 'ps',
          'axes.labelsize': 10,
          'axes.linewidth': 0.5,
          #'text.fontsize': 11,
          'figure.titlesize': 10,
          'legend.fontsize': 10,
          'xtick.labelsize': 10,
          'ytick.labelsize': 10,
          'text.usetex': True,
          'figure.figsize': fig_size}
pylab.rcParams.update(params)

nbrQQ = 500
rQQ = np.zeros(nbrQQ)
rQQmin = 0.0
rQQmax = 0.999
for i in range(nbrQQ):
    rQQ[i] = (1.-10**(np.log10(1-rQQmin) + (np.log10(1-rQQmax)-np.log10(1-rQQmin))/(nbrQQ-1)*i))*M
beta_0 = np.zeros(nbrQQ)
beta_1 = np.zeros(nbrQQ)
beta_2 = np.zeros(nbrQQ)
beta_12 = np.zeros(nbrQQ)
for i in range(nbrQQ):
    beta_0[i] = 4.*rplus(M,rQQ[i])**2
    beta_1[i] = 2.*rplus(M,rQQ[i])**2*(rplus(M,rQQ[i])-rminus(M,rQQ[i]))**2*2./3.
    beta_2[i] = 4.*rplus(M,rQQ[i])**3*(rplus(M,rQQ[i])-rminus(M,rQQ[i]))**3/45.
    beta_12[i] = (rplus(M,rQQ[i])-rminus(M,rQQ[i]))**2/2.

f = pylab.figure(5)
f.clf()
ax = f.add_subplot(111)
ax.set_xscale("log")
ax.set_yscale("log")
ax.set_xlim(1e-3,1)
#ax.set_ylim(-2.7,1)
ax.set_xlabel("$1-Q/M$")
ax.set_ylabel("$\\beta_s^Q$")
ax.scatter(1.-fits0[::2,0],10**fits0[::2,2]/4.,marker = "+",s = 10,color = "blue",label = "${\\rm spin\,\,0}$")
ax.plot(1.-rQQ*2.,beta_0/4.,linewidth = 1,color = "blue")
ax.scatter(1.-fits1[::2,0],10**fits1[::2,2]/4.*3.,marker = "x",s = 10,color = "green",label = "${\\rm spin\,\,1}$")
ax.plot(1.-rQQ*2.,beta_1/4.*3.,linewidth = 1,color = "green")
ax.scatter(1.-fits2[::2,0],10**fits2[::2,2]/4.*45.,marker = "o",s = 10,color = "red",label = "${\\rm spin\,\,2}$")
ax.plot(1.-rQQ*2.,beta_2/4.*45.,linewidth = 1,color = "red")
ax.scatter(1.-fits12[::2,0],10**fits12[::2,2]*2.,marker = "s",s = 10,color = "purple",label = "${\\rm spin\,\,1/2}$")
ax.plot(1.-rQQ*2.,beta_12*2.,linewidth = 1,color = "purple")
ax.legend(loc = "best",ncol = 1)
f.tight_layout(rect = [0,0,1,1])
f.show()
f.savefig("blah")


##
pylab.close("all")
