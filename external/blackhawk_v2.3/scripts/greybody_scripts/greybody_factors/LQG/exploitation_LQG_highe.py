import numpy as np
import scipy.interpolate as scp
import scipy.integrate as sci
import pylab
from scipy.special import gamma
from scipy import stats

## Exploiting LQG BHs results
# Here give the path to your greybody factors for polymerized BHs with a_0 = 0.11
file_0 = ""
file_1 = ""
file_2 = ""
file_12 = ""

data_0 = np.genfromtxt(file_0)
data_1 = np.genfromtxt(file_1)
data_2 = np.genfromtxt(file_2)
data_12 = np.genfromtxt(file_12)

# Here give the path to your greybody factors for a Schwarzschild BH
file_0_comp = ""
file_1_comp = ""
file_2_comp = ""
file_12_comp = ""

data_0_comp = np.genfromtxt(file_0_comp)
data_1_comp = np.genfromtxt(file_1_comp)
data_2_comp = np.genfromtxt(file_2_comp)
data_12_comp = np.genfromtxt(file_12_comp)

enumber = len(data_0[0])
energies = np.zeros(enumber)
Emin = 0.01
Einter = 1.
Emax = 5.
for i in range(len(energies)):
    if i<100:
        energies[i] = 10**(np.log10(Emin) + (np.log10(Einter) - np.log10(Emin))/(100.)*i)
    else:
        energies[i] = Einter + (Emax - Einter)/99.*(i-100.)

index_min = 25
index_max = 150

nbe = 11
epsilon = np.logspace(0.,2.,nbe)

M = 0.5

a0 = 1e-10

def P(epsilon):
    return (np.sqrt(1+epsilon**2)-1)/(np.sqrt(1+epsilon**2)+1)

def rplus(M,epsilon):
    return 2*M/(1+P(epsilon))**2

def rminus(M,epsilon):
    return rplus(M,epsilon)*P(epsilon)**2

def m(M,epsilon):
    return M/(1+P(epsilon))**2

def T(M,epsilon,a0):
    return 4*m(M,epsilon)**3*(1-P(epsilon)**2)/(32*np.pi*m(M,epsilon)**4+2*np.pi*a0**2)

## spin 0 (LQG low)
fig_width_pt = 80*2.83465  # Get this from LaTeX using \showthe\columnwidth
inches_per_pt = 1.0/72.27               # Convert pt to inch
fig_width = fig_width_pt*inches_per_pt  # width in inches
fig_height_pt = 80*2.83465
fig_height = fig_height_pt*inches_per_pt     # height in inches
fig_size =  [fig_width,fig_height]
params = {'backend': 'ps',
          'axes.labelsize': 11,
          'axes.linewidth': 0.5,
          #'text.fontsize': 11,
          'figure.titlesize': 11,
          'legend.fontsize': 11,
          'xtick.labelsize': 11,
          'ytick.labelsize': 11,
          'text.usetex': True,
          'figure.figsize': fig_size}
pylab.rcParams.update(params)

# Here give the path to the low energy data ( 1e-6 < x < 1e-4)
file_0_low = ""
file_0_low_comp = ""

data_0_comp_low = np.genfromtxt(file_0_low_comp)
data_0_low = np.genfromtxt(file_0_low)

energies_low = np.zeros(50)
for i in range(50):
    energies_low[i] = 10**(-6 + (-4 - (-6))/(50 - 1)*i)

f = pylab.figure(0)
f.clf()
ax = f.add_subplot(111)
ax.set_xscale("linear")
ax.set_yscale("linear")
ax.set_xlim(-6,-4)
#ax.set_ylim(-,0)
ax.set_xlabel("$\log_{10}(x)$")
ax.set_ylabel("$\log_{10}(Q_0)$")
ax.scatter(np.log10(energies_low),np.log10(data_0_comp_low*(np.exp(energies_low/T(M,0,0))-1.)),s = 1,color = "black",label = "${\\rm (Schwarzschild)}$")
ax.scatter(np.log10(energies_low),np.log10(data_0_low[0,:]*(np.exp(energies_low/T(M,epsilon[0],a0))-1.)),s = 1,color = "blue",label = "$\\varepsilon = 10^{-1}$")
ax.scatter(np.log10(energies_low),np.log10(data_0_low[4,:]*(np.exp(energies_low/T(M,epsilon[4],a0))-1.)),s = 1,color = "green",label = "$\\varepsilon = 10^{-0.6}$")
ax.scatter(np.log10(energies_low),np.log10(data_0_low[9,:]*(np.exp(energies_low/T(M,epsilon[9],a0))-1.)),s = 1,color = "red",label = "$\\varepsilon = 10^{-0.1}$")

slope, intercept, r_value, p_value, stderr = stats.linregress(np.log10(energies_low),np.log10(data_0_comp_low*(np.exp(energies_low/T(M,0,0))-1.)))

slopes = np.zeros(nbe)
intercepts = np.zeros(nbe)

for i in range(0,nbe):
    result = stats.linregress(np.log10(energies_low),np.log10(data_0_low[i,:]*(np.exp(energies_low/T(M,epsilon[i],a0))-1.)))
    slopes[i] = result.slope
    intercepts[i] = result.intercept

ax.plot(np.log10(energies_low),slope*np.log10(energies_low)+intercept,linewidth = 2,color = "black",linestyle = "--")
ax.plot(np.log10(energies_low),slopes[0]*np.log10(energies_low)+intercepts[0],linewidth = 0.5,color = "blue")
ax.plot(np.log10(energies_low),slopes[4]*np.log10(energies_low)+intercepts[4],linewidth = 0.5,color = "green")
ax.plot(np.log10(energies_low),slopes[9]*np.log10(energies_low)+intercepts[9],linewidth = 0.5,color = "red")

ax.legend(loc = "best")
f.show()

f = pylab.figure(1)
f.clf()
ax = f.add_subplot(111)
ax.set_xscale("linear")
ax.set_yscale("linear")
ax.set_xlabel("$x$")
ax.set_ylabel("$Q_0$")
#ax.set_xlim(0,5)
#ax.set_ylim(0,1.5)
ax.scatter(energies[index_max:],data_0_comp[index_max:]*(np.exp(energies[index_max:]/T(M,0,0))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "black",label = "${\\rm (Schwarzschild)}$")
ax.scatter(energies[index_max:],data_0[0,index_max:]*(np.exp(energies[index_max:]/T(M,epsilon[0],a0))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "blue",label = "$\\varepsilon = 10^{-1}$")
ax.scatter(energies[index_max:],data_0[4,index_max:]*(np.exp(energies[index_max:]/T(M,epsilon[4],a0))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "green",label = "$\\varepsilon = 10^{-1}$")
ax.scatter(energies[index_max:],data_0[9,index_max:]*(np.exp(energies[index_max:]/T(M,epsilon[9],a0))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "red",label = "$\\varepsilon = 10^{-1}$")


limit = 0.
for i in range(index_max,len(data_0[0])):
    limit += data_0_comp[i]*(np.exp(energies[i]/T(M,0,0))-1.)/(27./4.*energies[i]**2)
limit = limit/(len(data_0[0])-index_max)

limits = np.zeros(nbe)
for j in range(nbe):
    for i in range(index_max,len(data_0[0])):
        limits[j] += data_0[j,i]*(np.exp(energies[i]/T(M,epsilon[j],a0))-1.)/(27./4.*energies[i]**2)
    limits[j] = limits[j]/(len(data_0[0])-index_max)

ax.axhline(y = limit,xmin = 0,xmax = 1,linewidth = 0.5,color = "black")
ax.axhline(y = limits[0],xmin = 0,xmax = 1,linewidth = 0.5,color = "blue")
ax.axhline(y = limits[4],xmin = 0,xmax = 1,linewidth = 0.5,color = "green")
ax.axhline(y = limits[9],xmin = 0,xmax = 1,linewidth = 0.5,color = "red")

ax.legend(loc = "best")
f.show()

# Here give the path to the fits file
spin_0_fits = ""

print("%15s%15s%15s%15s\n"%("epsilon/fits","a1","b1","limit"),file=open(spin_0_fits,"w"),end = "")
print("%15.5e%15.5e%15.5e%15.5e\n"%(0.,slope,intercept,limit),file=open(spin_0_fits,"a"),end = "")
for i in range(10):
    print("%15.5e%15.5e%15.5e%15.5e\n"%(epsilon[i],slopes[i],intercepts[i],limits[i]),file=open(spin_0_fits,"a"),end = "")

## spin 1 (LQG low)
fig_width_pt = 80*2.83465  # Get this from LaTeX using \showthe\columnwidth
inches_per_pt = 1.0/72.27               # Convert pt to inch
fig_width = fig_width_pt*inches_per_pt  # width in inches
fig_height_pt = 80*2.83465
fig_height = fig_height_pt*inches_per_pt     # height in inches
fig_size =  [fig_width,fig_height]
params = {'backend': 'ps',
          'axes.labelsize': 11,
          'axes.linewidth': 0.5,
          #'text.fontsize': 11,
          'figure.titlesize': 11,
          'legend.fontsize': 11,
          'xtick.labelsize': 11,
          'ytick.labelsize': 11,
          'text.usetex': True,
          'figure.figsize': fig_size}
pylab.rcParams.update(params)

# Here give the path to the low energy data ( 1e-6 < x < 1e-4)
file_1_low = ""
file_1_low_comp = ""

data_1_comp_low = np.genfromtxt(file_1_low_comp)
data_1_low = np.genfromtxt(file_1_low)

energies_low = np.zeros(50)
for i in range(50):
    energies_low[i] = 10**(-6 + (-4 - (-6))/(50 - 1)*i)

f = pylab.figure(0)
f.clf()
ax = f.add_subplot(111)
ax.set_xscale("linear")
ax.set_yscale("linear")
ax.set_xlim(-6,-4)
#ax.set_ylim(-,0)
ax.set_xlabel("$\log_{10}(x)$")
ax.set_ylabel("$\log_{10}(Q_1)$")
ax.scatter(np.log10(energies_low),np.log10(data_1_comp_low*(np.exp(energies_low/T(M,0,0))-1.)),s = 1,color = "black",label = "${\\rm (Schwarzschild)}$")
ax.scatter(np.log10(energies_low),np.log10(data_1_low[0,:]*(np.exp(energies_low/T(M,epsilon[0],a0))-1.)),s = 1,color = "blue",label = "$\\varepsilon = 10^{-1}$")
ax.scatter(np.log10(energies_low),np.log10(data_1_low[4,:]*(np.exp(energies_low/T(M,epsilon[4],a0))-1.)),s = 1,color = "green",label = "$\\varepsilon = 10^{-0.6}$")
ax.scatter(np.log10(energies_low),np.log10(data_1_low[9,:]*(np.exp(energies_low/T(M,epsilon[9],a0))-1.)),s = 1,color = "red",label = "$\\varepsilon = 10^{-0.1}$")

slope, intercept, r_value, p_value, stderr = stats.linregress(np.log10(energies_low),np.log10(data_1_comp_low*(np.exp(energies_low/T(M,0,0))-1.)))

slopes = np.zeros(nbe)
intercepts = np.zeros(nbe)

for i in range(0,nbe):
    result = stats.linregress(np.log10(energies_low),np.log10(data_1_low[i,:]*(np.exp(energies_low/T(M,epsilon[i],a0))-1.)))
    slopes[i] = result.slope
    intercepts[i] = result.intercept

ax.plot(np.log10(energies_low),slope*np.log10(energies_low)+intercept,linewidth = 2,color = "black")
ax.plot(np.log10(energies_low),slopes[0]*np.log10(energies_low)+intercepts[0],linewidth = 0.5,color = "blue")
ax.plot(np.log10(energies_low),slopes[4]*np.log10(energies_low)+intercepts[4],linewidth = 0.5,color = "green")
ax.plot(np.log10(energies_low),slopes[9]*np.log10(energies_low)+intercepts[9],linewidth = 0.5,color = "red")

ax.legend(loc = "best")
f.show()

f = pylab.figure(1)
f.clf()
ax = f.add_subplot(111)
ax.set_xscale("linear")
ax.set_yscale("linear")
ax.set_xlabel("$x$")
ax.set_ylabel("$Q_1$")
#ax.set_xlim(0,5)
#ax.set_ylim(0,1.5)
ax.scatter(energies[index_max:],data_1_comp[index_max:]*(np.exp(energies[index_max:]/T(M,0,0))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "black",label = "${\\rm (Schwarzschild)}$")
ax.scatter(energies[index_max:],data_1[0,index_max:]*(np.exp(energies[index_max:]/T(M,epsilon[0],a0))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "blue",label = "$\\varepsilon = 10^{-1}$")
ax.scatter(energies[index_max:],data_1[4,index_max:]*(np.exp(energies[index_max:]/T(M,epsilon[4],a0))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "green",label = "$\\varepsilon = 10^{-0.6}$")
ax.scatter(energies[index_max:],data_1[9,index_max:]*(np.exp(energies[index_max:]/T(M,epsilon[9],a0))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "red",label = "$\\varepsilon = 10^{-0.1}$")


limit = 0.
for i in range(index_max,len(data_1[0])):
    limit += data_1_comp[i]*(np.exp(energies[i]/T(M,0,0))-1.)/(27./4.*energies[i]**2)
limit = limit/(len(data_1[0])-index_max)

limits = np.zeros(nbe)
for j in range(nbe):
    for i in range(index_max,len(data_1[0])):
        limits[j] += data_1[j,i]*(np.exp(energies[i]/T(M,epsilon[j],a0))-1.)/(27./4.*energies[i]**2)
    limits[j] = limits[j]/(len(data_1[0])-index_max)

ax.axhline(y = limit,xmin = 0,xmax = 1,linewidth = 0.5,color = "black")
ax.axhline(y = limits[0],xmin = 0,xmax = 1,linewidth = 0.5,color = "blue")
ax.axhline(y = limits[4],xmin = 0,xmax = 1,linewidth = 0.5,color = "green")
ax.axhline(y = limits[9],xmin = 0,xmax = 1,linewidth = 0.5,color = "red")

ax.legend(loc = "best")
f.show()

# Here give the path to the fits file
spin_1_fits = ""

print("%15s%15s%15s%15s\n"%("epsilon/fits","a1","b1","limit"),file=open(spin_1_fits,"w"),end = "")
print("%15.5e%15.5e%15.5e%15.5e\n"%(0.,slope,intercept,limit),file=open(spin_1_fits,"a"),end = "")
for i in range(10):
    print("%15.5e%15.5e%15.5e%15.5e\n"%(epsilon[i],slopes[i],intercepts[i],limits[i]),file=open(spin_1_fits,"a"),end = "")

## spin 2 (LQG low)
fig_width_pt = 80*2.83465  # Get this from LaTeX using \showthe\columnwidth
inches_per_pt = 1.0/72.27               # Convert pt to inch
fig_width = fig_width_pt*inches_per_pt  # width in inches
fig_height_pt = 80*2.83465
fig_height = fig_height_pt*inches_per_pt     # height in inches
fig_size =  [fig_width,fig_height]
params = {'backend': 'ps',
          'axes.labelsize': 11,
          'axes.linewidth': 0.5,
          #'text.fontsize': 11,
          'figure.titlesize': 11,
          'legend.fontsize': 11,
          'xtick.labelsize': 11,
          'ytick.labelsize': 11,
          'text.usetex': True,
          'figure.figsize': fig_size}
pylab.rcParams.update(params)

# Here give the path to the low energy data ( 1e-6 < x < 1e-4)
file_2_low = ""
file_2_low_comp = ""

data_2_comp_low = np.genfromtxt(file_2_low_comp)
data_2_low = np.genfromtxt(file_2_low)


energies_low = np.zeros(50)
for i in range(50):
    energies_low[i] = 10**(-6 + (-4 - (-6))/(50 - 1)*i)

f = pylab.figure(0)
f.clf()
ax = f.add_subplot(111)
ax.set_xscale("linear")
ax.set_yscale("linear")
ax.set_xlim(-6,-4)
#ax.set_ylim(-,0)
ax.set_xlabel("$\log_{10}(x)$")
ax.set_ylabel("$\log_{10}(Q_2)$")
ax.scatter(np.log10(energies_low),np.log10(data_2_comp_low*(np.exp(energies_low/T(M,0,0))-1.)),s = 1,color = "black",label = "${\\rm (Schwarzschild)}$")
ax.scatter(np.log10(energies_low),np.log10(data_2_low[0,:]*(np.exp(energies_low/T(M,epsilon[0],a0))-1.)),s = 1,color = "blue",label = "$\\varepsilon = 10^{-1}$")
ax.scatter(np.log10(energies_low),np.log10(data_2_low[4,:]*(np.exp(energies_low/T(M,epsilon[4],a0))-1.)),s = 1,color = "green",label = "$\\varepsilon = 10^{-0.6}$")
ax.scatter(np.log10(energies_low),np.log10(data_2_low[9,:]*(np.exp(energies_low/T(M,epsilon[9],a0))-1.)),s = 1,color = "red",label = "$\\varepsilon = 10^{-0.1}$")

slope, intercept, r_value, p_value, stderr = stats.linregress(np.log10(energies_low),np.log10(data_2_comp_low*(np.exp(energies_low/T(M,0,0))-1.)))

slopes = np.zeros(nbe)
intercepts = np.zeros(nbe)

for i in range(0,nbe):
    result = stats.linregress(np.log10(energies_low),np.log10(data_2_low[i,:]*(np.exp(energies_low/T(M,epsilon[i],a0))-1.)))
    slopes[i] = result.slope
    intercepts[i] = result.intercept

ax.plot(np.log10(energies_low),slope*np.log10(energies_low)+intercept,linewidth = 2,color = "black")
ax.plot(np.log10(energies_low),slopes[0]*np.log10(energies_low)+intercepts[0],linewidth = 0.5,color = "blue")
ax.plot(np.log10(energies_low),slopes[4]*np.log10(energies_low)+intercepts[4],linewidth = 0.5,color = "green")
ax.plot(np.log10(energies_low),slopes[9]*np.log10(energies_low)+intercepts[9],linewidth = 0.5,color = "red")

ax.legend(loc = "best")
f.show()

f = pylab.figure(1)
f.clf()
ax = f.add_subplot(111)
ax.set_xscale("linear")
ax.set_yscale("linear")
ax.set_xlabel("$x$")
ax.set_ylabel("$Q_2$")
#ax.set_xlim(0,5)
#ax.set_ylim(0,1.5)
ax.scatter(energies[index_max:],data_2_comp[index_max:]*(np.exp(energies[index_max:]/T(M,0,0))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "black",label = "${\\rm (Schwarzschild)}$")
ax.scatter(energies[index_max:],data_2[0,index_max:]*(np.exp(energies[index_max:]/T(M,epsilon[0],a0))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "blue",label = "$\\varepsilon = 10^{-1}$")
ax.scatter(energies[index_max:],data_2[4,index_max:]*(np.exp(energies[index_max:]/T(M,epsilon[4],a0))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "green",label = "$\\varepsilon = 10^{-0.6}$")
ax.scatter(energies[index_max:],data_2[9,index_max:]*(np.exp(energies[index_max:]/T(M,epsilon[9],a0))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "red",label = "$\\varepsilon = 10^{-0.1}$")

limit = 0.
for i in range(index_max,len(data_2[0])):
    limit += data_2_comp[i]*(np.exp(energies[i]/T(M,0,0))-1.)/(27./4.*energies[i]**2)
limit = limit/(len(data_2[0])-index_max)

limits = np.zeros(nbe)
for j in range(nbe):
    for i in range(index_max,len(data_2[0])):
        limits[j] += data_2[j,i]*(np.exp(energies[i]/T(M,epsilon[j],a0))-1.)/(27./4.*energies[i]**2)
    limits[j] = limits[j]/(len(data_2[0])-index_max)

ax.axhline(y = limit,xmin = 0,xmax = 1,linewidth = 0.5,color = "black")
ax.axhline(y = limits[0],xmin = 0,xmax = 1,linewidth = 0.5,color = "blue")
ax.axhline(y = limits[4],xmin = 0,xmax = 1,linewidth = 0.5,color = "green")
ax.axhline(y = limits[9],xmin = 0,xmax = 1,linewidth = 0.5,color = "red")

ax.legend(loc = "best")
f.show()

# Here give the path to the fits file
spin_2_fits = ""

print("%15s%15s%15s%15s\n"%("epsilon/fits","a1","b1","limit"),file=open(spin_2_fits,"w"),end = "")
print("%15.5e%15.5e%15.5e%15.5e\n"%(0.,slope,intercept,limit),file=open(spin_2_fits,"a"),end = "")
for i in range(10):
    print("%15.5e%15.5e%15.5e%15.5e\n"%(epsilon[i],slopes[i],intercepts[i],limits[i]),file=open(spin_2_fits,"a"),end = "")

## spin 1/2 low (LQG)
fig_width_pt = 80*2.83465  # Get this from LaTeX using \showthe\columnwidth
inches_per_pt = 1.0/72.27               # Convert pt to inch
fig_width = fig_width_pt*inches_per_pt  # width in inches
fig_height_pt = 80*2.83465
fig_height = fig_height_pt*inches_per_pt     # height in inches
fig_size =  [fig_width,fig_height]
params = {'backend': 'ps',
          'axes.labelsize': 11,
          'axes.linewidth': 0.5,
          #'text.fontsize': 11,
          'figure.titlesize': 11,
          'legend.fontsize': 11,
          'xtick.labelsize': 11,
          'ytick.labelsize': 11,
          'text.usetex': True,
          'figure.figsize': fig_size}
pylab.rcParams.update(params)

# Here give the path to the low energy data ( 1e-6 < x < 1e-4)
file_12_low = ""
file_12_low_comp = ""

data_12_comp_low = np.genfromtxt(file_12_low_comp)
data_12_low = np.genfromtxt(file_12_low)

energies_low = np.zeros(50)
for i in range(50):
    energies_low[i] = 10**(-6 + (-4 - (-6))/(50 - 1)*i)

f = pylab.figure(0)
f.clf()
ax = f.add_subplot(111)
ax.set_xscale("linear")
ax.set_yscale("linear")
ax.set_xlim(-6,-4)
#ax.set_ylim(-,0)
ax.set_xlabel("$\log_{10}(x)$")
ax.set_ylabel("$\log_{10}(Q_{1/2})$")
ax.scatter(np.log10(energies_low),np.log10(data_12_comp_low*(np.exp(energies_low/T(M,0,0))+1.)),s = 1,color = "black",label = "${\\rm (Schwarzschild)}$")
ax.scatter(np.log10(energies_low),np.log10(data_12_low[0,:]*(np.exp(energies_low/T(M,epsilon[0],a0))+1.)),s = 1,color = "blue",label = "$\\varepsilon = 10^{-1}$")
ax.scatter(np.log10(energies_low),np.log10(data_12_low[4,:]*(np.exp(energies_low/T(M,epsilon[4],a0))+1.)),s = 1,color = "green",label = "$\\varepsilon = 10^{-0.6}$")
ax.scatter(np.log10(energies_low),np.log10(data_12_low[9,:]*(np.exp(energies_low/T(M,epsilon[9],a0))+1.)),s = 1,color = "red",label = "$\\varepsilon = 10^{-0.1}$")

slope, intercept, r_value, p_value, stderr = stats.linregress(np.log10(energies_low),np.log10(data_12_comp_low*(np.exp(energies_low/T(M,0,0))+1.)))

slopes = np.zeros(nbe)
intercepts = np.zeros(nbe)

for i in range(0,nbe):
    result = stats.linregress(np.log10(energies_low),np.log10(data_12_low[i,:]*(np.exp(energies_low/T(M,epsilon[i],a0))+1.)))
    slopes[i] = result.slope
    intercepts[i] = result.intercept

ax.plot(np.log10(energies_low),slope*np.log10(energies_low)+intercept,linewidth = 0.5,color = "black")
ax.plot(np.log10(energies_low),slopes[0]*np.log10(energies_low)+intercepts[0],linewidth = 0.5,color = "blue")
ax.plot(np.log10(energies_low),slopes[4]*np.log10(energies_low)+intercepts[4],linewidth = 0.5,color = "green")
ax.plot(np.log10(energies_low),slopes[9]*np.log10(energies_low)+intercepts[9],linewidth = 0.5,color = "red")

ax.legend(loc = "best")
f.show()

f = pylab.figure(1)
f.clf()
ax = f.add_subplot(111)
ax.set_xscale("linear")
ax.set_yscale("linear")
ax.set_xlabel("$x$")
ax.set_ylabel("$Q_{1/2}$")
#ax.set_xlim(0,5)
#ax.set_ylim(0,1.5)
ax.scatter(energies[index_max:],data_12_comp[index_max:]*(np.exp(energies[index_max:]/T(M,0,0))+1.)/(27./4.*energies[index_max:]**2),s = 1,color = "black",label = "${\\rm (Schwarzschild)}$")
ax.scatter(energies[index_max:],data_12[0,index_max:]*(np.exp(energies[index_max:]/T(M,epsilon[0],a0))+1.)/(27./4.*energies[index_max:]**2),s = 1,color = "blue",label = "$\\varepsilon = 10^{-1}$")
ax.scatter(energies[index_max:],data_12[4,index_max:]*(np.exp(energies[index_max:]/T(M,epsilon[4],a0))+1.)/(27./4.*energies[index_max:]**2),s = 1,color = "green",label = "$\\varepsilon = 10^{-1}$")
ax.scatter(energies[index_max:],data_12[9,index_max:]*(np.exp(energies[index_max:]/T(M,epsilon[9],a0))+1.)/(27./4.*energies[index_max:]**2),s = 1,color = "red",label = "$\\varepsilon = 10^{-1}$")

limit = 0.
for i in range(index_max,len(data_12[0])):
    limit += data_12_comp[i]*(np.exp(energies[i]/T(M,0,0))+1.)/(27./4.*energies[i]**2)
limit = limit/(len(data_12[0])-index_max)

limits = np.zeros(nbe)
for j in range(nbe):
    for i in range(index_max,len(data_12[0])):
        limits[j] += data_12[j,i]*(np.exp(energies[i]/T(M,epsilon[j],a0))+1.)/(27./4.*energies[i]**2)
    limits[j] = limits[j]/(len(data_12[0])-index_max)

ax.axhline(y = limit,xmin = 0,xmax = 1,linewidth = 0.5,color = "black")
ax.axhline(y = limits[0],xmin = 0,xmax = 1,linewidth = 0.5,color = "blue")
ax.axhline(y = limits[4],xmin = 0,xmax = 1,linewidth = 0.5,color = "green")
ax.axhline(y = limits[9],xmin = 0,xmax = 1,linewidth = 0.5,color = "red")

ax.legend(loc = "best")
f.show()

# Here give the path to the fits file
spin_12_fits = ""

print("%15s%15s%15s%15s\n"%("epsilon/fits","a1","b1","limit"),file=open(spin_12_fits,"w"),end = "")
print("%15.5e%15.5e%15.5e%15.5e\n"%(0.,slope,intercept,limit),file=open(spin_12_fits,"a"),end = "")
for i in range(10):
    print("%15.5e%15.5e%15.5e%15.5e\n"%(epsilon[i],slopes[i],intercepts[i],limits[i]),file=open(spin_12_fits,"a"),end = "")

## Comparison to theory for all spins, high energy (LQG)
fig_width_pt = 85*2.83465  # Get this from LaTeX using \showthe\columnwidth
inches_per_pt = 1.0/72.27               # Convert pt to inch
fig_width = fig_width_pt*inches_per_pt  # width in inches
fig_height_pt = 80*2.83465
fig_height = fig_height_pt*inches_per_pt     # height in inches
fig_size =  [fig_width,fig_height]
params = {'backend': 'ps',
          'axes.labelsize': 10,
          'axes.linewidth': 0.5,
          #'text.fontsize': 11,
          'figure.titlesize': 10,
          'legend.fontsize': 10,
          'xtick.labelsize': 10,
          'ytick.labelsize': 10,
          'text.usetex': True,
          'figure.figsize': fig_size}
pylab.rcParams.update(params)

f = pylab.figure(2)
f.clf()
ax = f.add_subplot(111)

fits_0 = np.genfromtxt(spin_0_fits,skip_header = 1)
fits_1 = np.genfromtxt(spin_1_fits,skip_header = 1)
fits_2 = np.genfromtxt(spin_2_fits,skip_header = 1)
fits_12 = np.genfromtxt(spin_12_fits,skip_header = 1)

a0 = 0.11

def orbit(Rp,P):
    return 1./6.*(3. - 4.*np.sqrt(P) + 3.*P)*Rp + ((9. + 6.*np.sqrt(P) + 10.*P + 6.*P**(3/2) + 9.*P**2)*Rp**2)/(6.*((27. + 27.*np.sqrt(P) - 63.*P - 190.*P**(3/2) - 63.*P**2 + 27.*P**(5/2) + 27.*P**3)*Rp**3 + 3.*np.sqrt(3)*1j*np.sqrt((np.sqrt(P) + P)**2*(225. + 238.*np.sqrt(P) - 49.*P - 636.*P**(3/2) - 49.*P**2 + 238.*P**(5/2) + 225*P**3)*Rp**6))**(1/3)) + 1./6.*((27. + 27.*np.sqrt(P) - 63.*P - 190.*P**(3/2) - 63.*P**2 + 27.*P**(5/2) + 27.*P**3)*Rp**3 + 3.*np.sqrt(3)*1j*np.sqrt((np.sqrt(P) + P)**2*(225. + 238.*np.sqrt(P) - 49.*P - 636.*P**(3/2) - 49.*P**2 + 238.*P**(5/2) + 225.*P**3)*Rp**6))**(1/3)

def G(r,eps):
    return (r-rplus(M,eps))*(r-rminus(M,eps))*(r+np.sqrt(rplus(M,eps)*rminus(M,eps)))**2/(r**4+a0**2)

nbre = 100
ee = np.zeros(nbre)
emin = 0
emax = 0.99999
for i in range(nbre):
    ee[i] = (1.-10**(np.log10(1-emin) + (np.log10(1-emax)-np.log10(1-emin))/(nbre-1)*i))
ratio = np.zeros(len(ee))
ratio2 = np.zeros(len(ee))
for i in range(len(ee)):
    ratio[i] = ((4.*m(M,ee[i]))**2 + a0**2/(4.*m(M,ee[i]))**2)/4.
    ratio2[i] = np.real(orbit(rplus(M,ee[i]),P(ee[i])))**2/G(np.real(orbit(rplus(M,ee[i]),P(ee[i]))),ee[i])*4./27.

# This is the numerically computed high energy limit for high epsilon
num = np.genfromtxt("High energy limit_highe.txt")

ax.set_xscale("log")
ax.set_xlabel("$\\varepsilon$")
ax.set_ylabel("$\\beta_{\infty}^{\\rm LQG}$")
ax.set_xlim(1e-1,1e+1)
ax.scatter(data0[:,0],data0[:,3],s = 10,marker = "+",color = "black")#,label = "${\\rm spin\,\,0}$")
ax.scatter(data1[:,0],data1[:,3],s = 10,marker = "x",color = "black")#,label = "${\\rm spin\,\,1}$")
ax.scatter(data2[:,0],data2[:,3],s = 10,marker = "o",color = "black")#,label = "${\\rm spin\,\,2}$")
ax.scatter(data12[:,0],data12[:,3],s = 10,marker = "s",color = "black")#,label = "${\\rm spin\,\,1/2}$")
ax.scatter(data0_highe[:,0],data0_highe[:,3],s = 10,marker = "+",color = "black",label = "${\\rm spin\,\,0}$")
ax.scatter(data1_highe[:,0],data1_highe[:,3],s = 10,marker = "x",color = "black",label = "${\\rm spin\,\,1}$")
ax.scatter(data2_highe[:,0],data2_highe[:,3],s = 10,marker = "o",color = "black",label = "${\\rm spin\,\,2}$")
ax.scatter(data12_highe[:,0],data12_highe[:,3],s = 10,marker = "s",color = "black",label = "${\\rm spin\,\,1/2}$")
ax.plot(num[::2],num[1::2],color = "red",linewidth = 1,label = "${\\rm theory}$")
ax.legend(loc = "best")

f.tight_layout(rect = [0,0,1,1])
f.show()
f.savefig("")

## Comparison between all spins for low energy (LQG)
fig_width_pt = 85*2.83465  # Get this from LaTeX using \showthe\columnwidth
inches_per_pt = 1.0/72.27               # Convert pt to inch
fig_width = fig_width_pt*inches_per_pt  # width in inches
fig_height_pt = 80*2.83465
fig_height = fig_height_pt*inches_per_pt     # height in inches
fig_size =  [fig_width,fig_height]
params = {'backend': 'ps',
          'axes.labelsize': 10,
          'axes.linewidth': 0.5,
          #'text.fontsize': 11,
          'figure.titlesize': 10,
          'legend.fontsize': 10,
          'xtick.labelsize': 10,
          'ytick.labelsize': 10,
          'text.usetex': True,
          'figure.figsize': fig_size}
pylab.rcParams.update(params)

a0 = 1e-10

nbre = 100
ee = np.logspace(-1,2,nbre)
beta_0 = np.zeros(len(ee))
beta_0_bis = np.zeros(len(ee))
for i in range(len(ee)):
    beta_0[i] = 16.*m(M,ee[i])**2*(1+a0**2/(16.*m(M,ee[i])**4))*(1+P(ee[i])**2)/(4.*rplus(M,0)**2)
    beta_0_bis[i] = rplus(M,ee[i])*(rplus(M,ee[i]) + rminus(M,ee[i]))


f = pylab.figure(5)
f.clf()
ax = f.add_subplot(111)
ax.set_xscale("linear")
ax.set_yscale("log")
ax.set_xlim(0,1)
ax.set_ylim(1e-2,2e+0)
ax.set_xlabel("$\\varepsilon$")
ax.set_ylabel("$\\beta_s^{\\rm LQG}$")
ax.scatter(fits_0[:,0],10**fits_0[:,2]/4.,marker = "+",s = 10,color = "blue",label = "${\\rm spin\,\,0}$")
ax.plot(ee,beta_0,linewidth = 1,color = "blue")
ax.scatter(fits_1[:,0],10**fits_1[:,2]/4.*3.,marker = "x",s = 10,color = "green",label = "${\\rm spin\,\,1}$")
ax.scatter(fits_2[:,0],10**fits_2[:,2]/4.*45.,marker = "o",s = 10,color = "red",label = "${\\rm spin\,\,2}$")
ax.scatter(fits_12[:,0],10**fits_12[:,2]*2.,marker = "s",s = 10,color = "purple",label = "${\\rm spin\,\,1/2}$")
ax.legend(loc = "best",ncol = 1)
f.tight_layout(rect = [0,0,1,1])
f.show()
#f.savefig("")

## Creating the formatted tables
enumber = len(data_0[0])
energies = np.zeros(enumber)
Emin = 0.01
Einter = 1.
Emax = 5.
for i in range(len(energies)):
    if i<100:
        energies[i] = 10**(np.log10(Emin) + (np.log10(Einter) - np.log10(Emin))/(100.)*i)
    else:
        energies[i] = Einter + (Emax - Einter)/99.*(i-100.)

epsilon = np.logspace(0.,2.,len(data_0))

# Here enter the paths for the formatted greybody factors tables
spin_0_format = ""
spin_1_format = ""
spin_2_format = ""
spin_12_format = ""

print("%15s"%("epsilon/x"),file=open(spin_0_format,"w"),end = "")
for i in range(len(energies)):
    print("%15.5e"%(energies[i]),file=open(spin_0_format,"a"),end = "")
print("\n",file=open(spin_0_format,"a"),end = "")
print("%15.5f"%(0.),file=open(spin_0_format,"a"),end = "")
for i in range(len(data_0_comp)):
    print("%15.5e"%(data_0_comp[i]),file=open(spin_0_format,"a"),end = "")
print("\n",file=open(spin_0_format,"a"),end = "")
for i in range(len(epsilon)):
    print("%15.5f"%(epsilon[i]),file=open(spin_0_format,"a"),end = "")
    for j in range(len(data_0[0])):
        print("%15.5e"%(data_0[i,j]),file=open(spin_0_format,"a"),end = "")
    print("\n",file=open(spin_0_format,"a"),end = "")

print("%15s"%("epsilon/x"),file=open(spin_1_format,"w"),end = "")
for i in range(len(energies)):
    print("%15.5e"%(energies[i]),file=open(spin_1_format,"a"),end = "")
print("\n",file=open(spin_1_format,"a"),end = "")
print("%15.5f"%(0.),file=open(spin_1_format,"a"),end = "")
for i in range(len(data_1_comp)):
    print("%15.5e"%(data_1_comp[i]),file=open(spin_1_format,"a"),end = "")
print("\n",file=open(spin_1_format,"a"),end = "")
for i in range(len(epsilon)):
    print("%15.5f"%(epsilon[i]),file=open(spin_1_format,"a"),end = "")
    for j in range(len(data_1[0])):
        print("%15.5e"%(data_1[i,j]),file=open(spin_1_format,"a"),end = "")
    print("\n",file=open(spin_1_format,"a"),end = "")

print("%15s"%("epsilon/x"),file=open(spin_2_format,"w"),end = "")
for i in range(len(energies)):
    print("%15.5e"%(energies[i]),file=open(spin_2_format,"a"),end = "")
print("\n",file=open(spin_2_format,"a"),end = "")
print("%15.5f"%(0.),file=open(spin_2_format,"a"),end = "")
for i in range(len(data_2_comp)):
    print("%15.5e"%(data_2_comp[i]),file=open(spin_2_format,"a"),end = "")
print("\n",file=open(spin_2_format,"a"),end = "")
for i in range(len(epsilon)):
    print("%15.5f"%(epsilon[i]),file=open(spin_2_format,"a"),end = "")
    for j in range(len(data_2[0])):
        print("%15.5e"%(data_2[i,j]),file=open(spin_2_format,"a"),end = "")
    print("\n",file=open(spin_2_format,"a"),end = "")

print("%15s"%("epsilon/x"),file=open(spin_12_format,"w"),end = "")
for i in range(len(energies)):
    print("%15.5e"%(energies[i]),file=open(spin_12_format,"a"),end = "")
print("\n",file=open(spin_12_format,"a"),end = "")
print("%15.5f"%(0.),file=open(spin_12_format,"a"),end = "")
for i in range(len(data_12_comp)):
    print("%15.5e"%(data_12_comp[i]),file=open(spin_12_format,"a"),end = "")
print("\n",file=open(spin_12_format,"a"),end = "")
for i in range(len(epsilon)):
    print("%15.5f"%(epsilon[i]),file=open(spin_12_format,"a"),end = "")
    for j in range(len(data_12[0])):
        print("%15.5e"%(data_12[i,j]),file=open(spin_12_format,"a"),end = "")
    print("\n",file=open(spin_12_format,"a"),end = "")