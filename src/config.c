// Configuration management for unified black hole evolution
// Handles parameter files, validation, and default settings

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>
#include <sys/stat.h>
#include "evolution.h"

#define MAX_LINE_LENGTH 1024
#define MAX_KEY_LENGTH 128
#define MAX_VALUE_LENGTH 256

// Helper function to trim whitespace
char* trim_whitespace(char* str) {
    char* end;
    
    // Trim leading space
    while (*str == ' ' || *str == '\t') str++;
    
    if (*str == 0) return str;
    
    // Trim trailing space
    end = str + strlen(str) - 1;
    while (end > str && (*end == ' ' || *end == '\t' || *end == '\n' || *end == '\r')) end--;
    
    end[1] = '\0';
    return str;
}

// Parse a key-value pair from a line
int parse_config_line(const char* line, char* key, char* value) {
    if (!line || !key || !value) return 0;
    
    // Skip comments and empty lines
    if (line[0] == '#' || line[0] == '\0' || line[0] == '\n') return 0;
    
    // Find the '=' separator
    const char* equals = strchr(line, '=');
    if (!equals) return 0;
    
    // Extract key
    size_t key_len = equals - line;
    if (key_len >= MAX_KEY_LENGTH) return 0;
    strncpy(key, line, key_len);
    key[key_len] = '\0';
    
    // Extract value
    const char* value_start = equals + 1;
    if (strlen(value_start) >= MAX_VALUE_LENGTH) return 0;
    strcpy(value, value_start);
    
    // Trim whitespace
    strcpy(key, trim_whitespace(key));
    strcpy(value, trim_whitespace(value));
    
    return 1;
}

// Set configuration parameter
EvolutionError set_config_parameter(EvolutionConfig* config, const char* key, const char* value) {
    if (!config || !key || !value) return EVOLUTION_ERROR_CONFIG;
    
    // Evolution parameters
    if (strcmp(key, "t_initial") == 0) {
        config->t_initial = atof(value);
    } else if (strcmp(key, "t_final") == 0) {
        config->t_final = atof(value);
    } else if (strcmp(key, "dt_initial") == 0) {
        config->dt_initial = atof(value);
    } else if (strcmp(key, "dt_min") == 0) {
        config->dt_min = atof(value);
    } else if (strcmp(key, "dt_max") == 0) {
        config->dt_max = atof(value);
    } else if (strcmp(key, "tolerance") == 0) {
        config->tolerance = atof(value);
    }
    
    // Physics parameters
    else if (strcmp(key, "enable_hawking") == 0) {
        config->enable_hawking = atoi(value);
    } else if (strcmp(key, "enable_superradiance") == 0) {
        config->enable_superradiance = atoi(value);
    } else if (strcmp(key, "scalar_mass") == 0) {
        // Assume input is in eV, convert to kg
        double mass_eV = atof(value);
        config->scalar_mass = mass_eV * 1.78266192e-27; // eV to kg
    } else if (strcmp(key, "scalar_mass_kg") == 0) {
        config->scalar_mass = atof(value);
    } else if (strcmp(key, "l_max") == 0) {
        config->l_max = atoi(value);
    } else if (strcmp(key, "m_max") == 0) {
        config->m_max = atoi(value);
    } else if (strcmp(key, "n_max") == 0) {
        config->n_max = atoi(value);
    } else if (strcmp(key, "initial_cloud_fraction") == 0) {
        config->initial_cloud_fraction = atof(value);
    }
    
    // Numerical parameters
    else if (strcmp(key, "adaptive_timestep") == 0) {
        config->adaptive_timestep = atoi(value);
    } else if (strcmp(key, "max_iterations") == 0) {
        config->max_iterations = atoi(value);
    } else if (strcmp(key, "convergence_threshold") == 0) {
        config->convergence_threshold = atof(value);
    } else if (strcmp(key, "integration_method") == 0) {
        config->integration_method = atoi(value);
    }
    
    // Output parameters
    else if (strcmp(key, "output_dir") == 0) {
        strncpy(config->output_dir, value, sizeof(config->output_dir) - 1);
        config->output_dir[sizeof(config->output_dir) - 1] = '\0';
    } else if (strcmp(key, "output_frequency") == 0) {
        config->output_frequency = atoi(value);
    } else if (strcmp(key, "verbose") == 0) {
        config->verbose = atoi(value);
    } else if (strcmp(key, "save_intermediate") == 0) {
        config->save_intermediate = atoi(value);
    }
    
    // Cache parameters
    else if (strcmp(key, "enable_cache") == 0) {
        config->enable_cache = atoi(value);
    } else if (strcmp(key, "cache_size") == 0) {
        config->cache_size = atoi(value);
    } else if (strcmp(key, "cache_file") == 0) {
        strncpy(config->cache_file, value, sizeof(config->cache_file) - 1);
        config->cache_file[sizeof(config->cache_file) - 1] = '\0';
    }
    
    else {
        if (config->verbose > 0) {
            printf("Warning: Unknown configuration parameter '%s'\n", key);
        }
        return EVOLUTION_ERROR_CONFIG;
    }
    
    return EVOLUTION_SUCCESS;
}

// Load configuration from file
EvolutionError load_config(const char* filename, EvolutionConfig* config) {
    if (!filename || !config) return EVOLUTION_ERROR_CONFIG;
    
    // Set defaults first
    set_default_config(config);
    
    FILE* file = fopen(filename, "r");
    if (!file) {
        printf("Warning: Could not open config file '%s', using defaults\n", filename);
        return EVOLUTION_SUCCESS; // Not an error, just use defaults
    }
    
    char line[MAX_LINE_LENGTH];
    char key[MAX_KEY_LENGTH];
    char value[MAX_VALUE_LENGTH];
    int line_number = 0;
    
    while (fgets(line, sizeof(line), file)) {
        line_number++;
        
        if (parse_config_line(line, key, value)) {
            EvolutionError error = set_config_parameter(config, key, value);
            if (error != EVOLUTION_SUCCESS && config->verbose > 0) {
                printf("Warning: Error parsing line %d in config file '%s'\n", 
                       line_number, filename);
            }
        }
    }
    
    fclose(file);
    
    // Validate configuration
    return validate_config(config);
}

// Validate configuration parameters
EvolutionError validate_config(const EvolutionConfig* config) {
    if (!config) return EVOLUTION_ERROR_CONFIG;
    
    // Check time parameters
    if (config->t_final <= config->t_initial) {
        printf("Error: t_final must be greater than t_initial\n");
        return EVOLUTION_ERROR_CONFIG;
    }
    
    if (config->dt_initial <= 0 || config->dt_min <= 0 || config->dt_max <= 0) {
        printf("Error: All timestep parameters must be positive\n");
        return EVOLUTION_ERROR_CONFIG;
    }
    
    if (config->dt_min > config->dt_max) {
        printf("Error: dt_min must be less than or equal to dt_max\n");
        return EVOLUTION_ERROR_CONFIG;
    }
    
    if (config->dt_initial < config->dt_min || config->dt_initial > config->dt_max) {
        printf("Error: dt_initial must be between dt_min and dt_max\n");
        return EVOLUTION_ERROR_CONFIG;
    }
    
    // Check physics parameters
    if (config->scalar_mass < 0) {
        printf("Error: scalar_mass must be non-negative\n");
        return EVOLUTION_ERROR_CONFIG;
    }
    
    if (config->l_max < 0 || config->m_max < 0 || config->n_max < 0) {
        printf("Error: Quantum numbers must be non-negative\n");
        return EVOLUTION_ERROR_CONFIG;
    }
    
    if (config->initial_cloud_fraction < 0 || config->initial_cloud_fraction > 1) {
        printf("Error: initial_cloud_fraction must be between 0 and 1\n");
        return EVOLUTION_ERROR_CONFIG;
    }
    
    // Check numerical parameters
    if (config->max_iterations <= 0) {
        printf("Error: max_iterations must be positive\n");
        return EVOLUTION_ERROR_CONFIG;
    }
    
    if (config->convergence_threshold <= 0) {
        printf("Error: convergence_threshold must be positive\n");
        return EVOLUTION_ERROR_CONFIG;
    }
    
    if (config->integration_method < 0 || config->integration_method > 2) {
        printf("Error: integration_method must be 0, 1, or 2\n");
        return EVOLUTION_ERROR_CONFIG;
    }
    
    // Check output parameters
    if (config->output_frequency <= 0) {
        printf("Error: output_frequency must be positive\n");
        return EVOLUTION_ERROR_CONFIG;
    }
    
    // Check cache parameters
    if (config->cache_size <= 0) {
        printf("Error: cache_size must be positive\n");
        return EVOLUTION_ERROR_CONFIG;
    }
    
    return EVOLUTION_SUCCESS;
}

// Print configuration
void print_config(const EvolutionConfig* config) {
    if (!config) return;
    
    printf("Evolution Configuration:\n");
    printf("========================\n");
    printf("Time parameters:\n");
    printf("  t_initial = %.3e s\n", config->t_initial);
    printf("  t_final = %.3e s\n", config->t_final);
    printf("  dt_initial = %.3e s\n", config->dt_initial);
    printf("  dt_min = %.3e s\n", config->dt_min);
    printf("  dt_max = %.3e s\n", config->dt_max);
    printf("  tolerance = %.3e\n", config->tolerance);
    
    printf("\nPhysics parameters:\n");
    printf("  enable_hawking = %d\n", config->enable_hawking);
    printf("  enable_superradiance = %d\n", config->enable_superradiance);
    printf("  scalar_mass = %.3e kg (%.3e eV)\n", 
           config->scalar_mass, config->scalar_mass / 1.78266192e-27);
    printf("  l_max = %d\n", config->l_max);
    printf("  m_max = %d\n", config->m_max);
    printf("  n_max = %d\n", config->n_max);
    printf("  initial_cloud_fraction = %.3e\n", config->initial_cloud_fraction);
    
    printf("\nNumerical parameters:\n");
    printf("  adaptive_timestep = %d\n", config->adaptive_timestep);
    printf("  max_iterations = %d\n", config->max_iterations);
    printf("  convergence_threshold = %.3e\n", config->convergence_threshold);
    printf("  integration_method = %d\n", config->integration_method);
    
    printf("\nOutput parameters:\n");
    printf("  output_dir = %s\n", config->output_dir);
    printf("  output_frequency = %d\n", config->output_frequency);
    printf("  verbose = %d\n", config->verbose);
    printf("  save_intermediate = %d\n", config->save_intermediate);
    
    printf("\nCache parameters:\n");
    printf("  enable_cache = %d\n", config->enable_cache);
    printf("  cache_size = %d\n", config->cache_size);
    printf("  cache_file = %s\n", config->cache_file);
    printf("\n");
}

// Create output directory if it doesn't exist
EvolutionError create_output_directory(const char* dir_path) {
    if (!dir_path) return EVOLUTION_ERROR_CONFIG;
    
    struct stat st = {0};
    if (stat(dir_path, &st) == -1) {
        if (mkdir(dir_path, 0755) != 0) {
            printf("Error: Could not create output directory '%s'\n", dir_path);
            return EVOLUTION_ERROR_IO;
        }
    }
    
    return EVOLUTION_SUCCESS;
}
