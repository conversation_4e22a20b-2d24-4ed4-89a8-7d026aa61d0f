// Main evolution driver for unified black hole evolution
// Combines Hawking radiation and superradiance effects

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>
#include <time.h>
#include "evolution.h"
#include "qnm_interface.h"
#include "superradiance.h"

#define PI 3.14159265358979323846
#define G_NEWTON 6.67430e-11  // m^3 kg^-1 s^-2
#define C_LIGHT 2.99792458e8   // m/s
#define HBAR 1.054571817e-34   // J⋅s
#define M_PLANCK 2.176434e-8   // kg

// Set default configuration
void set_default_config(EvolutionConfig *config) {
    // Evolution parameters
    config->t_initial = 0.0;
    config->t_final = 1e15;  // ~300 million years
    config->dt_initial = 1e10; // ~300 years
    config->dt_min = 1e6;    // ~10 days
    config->dt_max = 1e12;   // ~30,000 years
    config->tolerance = 1e-6;
    
    // Physics parameters
    config->enable_hawking = 1;
    config->enable_superradiance = 1;
    config->scalar_mass = 1e-12 * 1.78266192e-27; // 1e-12 eV in kg
    config->l_max = 3;
    config->m_max = 3;
    config->n_max = 0;  // Only fundamental modes
    config->initial_cloud_fraction = 1e-6;
    
    // Numerical parameters
    config->adaptive_timestep = 1;
    config->max_iterations = 1000000;
    config->convergence_threshold = 1e-10;
    config->integration_method = 1; // RK4
    
    // Output parameters
    strcpy(config->output_dir, "./results");
    config->output_frequency = 100;
    config->verbose = 1;
    config->save_intermediate = 1;
    
    // Cache parameters
    config->enable_cache = 1;
    config->cache_size = 10000;
    strcpy(config->cache_file, "./qnm_cache.dat");
}

// Initialize black hole state
EvolutionError initialize_black_hole_state(BlackHoleState *bh, EvolutionConfig *config) {
    if (!bh || !config) return EVOLUTION_ERROR_CONFIG;
    
    // Initialize state
    bh->t = config->t_initial;
    bh->dt = config->dt_initial;
    bh->mu_scalar = config->scalar_mass;
    bh->cloud_mass = bh->M * config->initial_cloud_fraction;
    bh->cloud_energy = bh->cloud_mass * C_LIGHT * C_LIGHT;
    
    // Initialize rates
    bh->dM_dt_hawking = 0.0;
    bh->da_dt_hawking = 0.0;
    bh->dM_dt_super = 0.0;
    bh->da_dt_super = 0.0;
    bh->dM_dt_total = 0.0;
    bh->da_dt_total = 0.0;
    
    // Initialize diagnostics
    bh->total_energy_radiated = 0.0;
    bh->total_angular_momentum_lost = 0.0;
    bh->energy_conservation_error = 0.0;
    bh->evolution_step = 0;
    
    // Initialize flags
    bh->converged = 0;
    bh->stable = 1;
    
    return EVOLUTION_SUCCESS;
}

// Calculate Hawking radiation rates (placeholder - needs BlackHawk integration)
EvolutionError calculate_hawking_rates(const BlackHoleState *bh, 
                                      HawkingInterface *hawking,
                                      double *dM_dt, double *da_dt) {
    if (!bh || !dM_dt || !da_dt) return EVOLUTION_ERROR_CONFIG;
    
    // Placeholder implementation - replace with actual BlackHawk calls
    // Hawking radiation rate: dM/dt ~ -1/M^2 (in Planck units)
    double M_planck = bh->M / M_PLANCK;
    double hawking_rate = -1.0 / (M_planck * M_planck); // Planck units
    
    // Convert to SI units
    *dM_dt = hawking_rate * M_PLANCK * M_PLANCK * M_PLANCK / 
             (HBAR * C_LIGHT * C_LIGHT); // kg/s
    
    // Spin evolution (simplified)
    double a_norm = bh->a;
    *da_dt = -2.0 * (*dM_dt) * a_norm / bh->M; // Approximate
    
    return EVOLUTION_SUCCESS;
}

// Calculate superradiance rates
EvolutionError calculate_superradiance_rates(const BlackHoleState *bh,
                                           const EvolutionConfig *config,
                                           double *dM_dt, double *da_dt) {
    if (!bh || !config || !dM_dt || !da_dt) return EVOLUTION_ERROR_CONFIG;
    
    *dM_dt = 0.0;
    *da_dt = 0.0;
    
    if (!config->enable_superradiance) return EVOLUTION_SUCCESS;
    
    // Check if superradiance is possible
    if (!can_superradiate(bh->M, bh->a, bh->mu_scalar)) {
        return EVOLUTION_SUCCESS;
    }
    
    // Calculate total superradiance from all modes
    SuperradianceTotalRate total_rate = calculate_total_superradiance(
        bh->M, bh->a, bh->mu_scalar, 
        config->l_max, config->n_max, bh->cloud_mass);
    
    *dM_dt = total_rate.total_mass_rate;
    *da_dt = total_rate.total_spin_rate;
    
    return EVOLUTION_SUCCESS;
}

// Adaptive timestep calculation
double calculate_adaptive_timestep(const BlackHoleState *bh, const EvolutionConfig *config) {
    if (!bh || !config) return config->dt_initial;
    
    double dt_hawking = estimate_hawking_timescale(bh);
    double dt_super = estimate_superradiance_timescale(bh);
    double dt_stability = estimate_numerical_stability_limit(bh);
    
    // Take minimum of all timescales
    double dt_new = fmin(fmin(dt_hawking, dt_super), dt_stability) * 0.1;
    
    // Apply constraints
    dt_new = fmax(dt_new, config->dt_min);
    dt_new = fmin(dt_new, config->dt_max);
    
    // Smooth timestep changes to avoid oscillations
    if (dt_new > 2.0 * bh->dt) dt_new = 2.0 * bh->dt;
    if (dt_new < 0.5 * bh->dt) dt_new = 0.5 * bh->dt;
    
    return dt_new;
}

// Estimate timescales
double estimate_hawking_timescale(const BlackHoleState *bh) {
    if (!bh || bh->M <= 0) return 1e20; // Very large if invalid
    
    // Hawking timescale: t_H ~ M^3 (in Planck units)
    double M_planck = bh->M / M_PLANCK;
    double t_planck = HBAR / (M_PLANCK * C_LIGHT * C_LIGHT);
    
    return M_planck * M_planck * M_planck * t_planck;
}

double estimate_superradiance_timescale(const BlackHoleState *bh) {
    if (!bh || bh->mu_scalar <= 0) return 1e20;
    
    // Superradiance timescale: t_SR ~ 1/(mu * alpha^7)
    double alpha = G_NEWTON * bh->M * bh->mu_scalar / (HBAR * C_LIGHT);
    if (alpha <= 0) return 1e20;
    
    double alpha7 = pow(alpha, 7);
    return 1.0 / (bh->mu_scalar * alpha7 / HBAR);
}

double estimate_numerical_stability_limit(const BlackHoleState *bh) {
    if (!bh) return 1e10;
    
    // Stability limit based on relative change rates
    double max_rate = fmax(fabs(bh->dM_dt_total), fabs(bh->da_dt_total));
    if (max_rate <= 0) return 1e15;
    
    // Limit relative change to 1% per timestep
    return 0.01 / max_rate;
}

// Single evolution step using Euler method
EvolutionError euler_step(BlackHoleState *bh, double dt, 
                         double dM_dt, double da_dt) {
    if (!bh) return EVOLUTION_ERROR_CONFIG;
    
    // Update state
    bh->M += dM_dt * dt;
    bh->a += da_dt * dt;
    bh->t += dt;
    
    // Check physical bounds
    if (bh->M <= 0) return EVOLUTION_ERROR_PHYSICS;
    if (bh->a < 0) bh->a = 0.0;  // Clamp to physical range
    if (bh->a > 0.998) bh->a = 0.998; // Avoid extremal limit
    
    return EVOLUTION_SUCCESS;
}

// Update diagnostics
void update_diagnostics(BlackHoleState *bh, double dt) {
    if (!bh) return;
    
    // Update energy and angular momentum loss
    double energy_loss = -bh->dM_dt_total * C_LIGHT * C_LIGHT * dt;
    bh->total_energy_radiated += energy_loss;
    
    double L = bh->a * bh->M * bh->M * G_NEWTON / C_LIGHT;
    double dL_dt = 2.0 * bh->a * bh->M * bh->dM_dt_total + 
                   bh->M * bh->M * bh->da_dt_total;
    bh->total_angular_momentum_lost += -dL_dt * dt;
    
    // Calculate energy conservation error
    bh->energy_conservation_error = calculate_energy_conservation_error(bh);
    
    bh->evolution_step++;
}

// Main evolution step
EvolutionError evolution_step(BlackHoleState *bh, EvolutionConfig *config, 
                             HawkingInterface *hawking) {
    if (!bh || !config) return EVOLUTION_ERROR_CONFIG;
    
    EvolutionError error;
    
    // Calculate rates
    double dM_dt_hawking, da_dt_hawking;
    double dM_dt_super, da_dt_super;
    
    error = calculate_hawking_rates(bh, hawking, &dM_dt_hawking, &da_dt_hawking);
    if (error != EVOLUTION_SUCCESS) return error;
    
    error = calculate_superradiance_rates(bh, config, &dM_dt_super, &da_dt_super);
    if (error != EVOLUTION_SUCCESS) return error;
    
    // Store rates
    bh->dM_dt_hawking = dM_dt_hawking;
    bh->da_dt_hawking = da_dt_hawking;
    bh->dM_dt_super = dM_dt_super;
    bh->da_dt_super = da_dt_super;
    bh->dM_dt_total = dM_dt_hawking + dM_dt_super;
    bh->da_dt_total = da_dt_hawking + da_dt_super;
    
    // Adaptive timestep
    if (config->adaptive_timestep) {
        bh->dt = calculate_adaptive_timestep(bh, config);
    }
    
    // Integration step
    error = euler_step(bh, bh->dt, bh->dM_dt_total, bh->da_dt_total);
    if (error != EVOLUTION_SUCCESS) return error;
    
    // Update diagnostics
    update_diagnostics(bh, bh->dt);
    
    return EVOLUTION_SUCCESS;
}

// Calculate energy conservation error
double calculate_energy_conservation_error(const BlackHoleState *bh) {
    if (!bh) return 1.0;

    // Simple energy conservation check
    // Total energy should be conserved: E_BH + E_cloud + E_radiated = constant
    double E_bh = bh->M * C_LIGHT * C_LIGHT;
    double E_cloud = bh->cloud_energy;
    double E_radiated = bh->total_energy_radiated;

    // Initial energy (approximate)
    double E_initial = E_bh + E_cloud + E_radiated;
    double E_current = E_bh + E_cloud;

    if (E_initial > 0) {
        return fabs(E_current - E_initial) / E_initial;
    }
    return 0.0;
}

// I/O functions
EvolutionError save_state(const BlackHoleState *bh, const char *filename) {
    if (!bh || !filename) return EVOLUTION_ERROR_CONFIG;

    FILE *file = fopen(filename, "wb");
    if (!file) return EVOLUTION_ERROR_IO;

    size_t written = fwrite(bh, sizeof(BlackHoleState), 1, file);
    fclose(file);

    return (written == 1) ? EVOLUTION_SUCCESS : EVOLUTION_ERROR_IO;
}

EvolutionError load_state(BlackHoleState *bh, const char *filename) {
    if (!bh || !filename) return EVOLUTION_ERROR_CONFIG;

    FILE *file = fopen(filename, "rb");
    if (!file) return EVOLUTION_ERROR_IO;

    size_t read = fread(bh, sizeof(BlackHoleState), 1, file);
    fclose(file);

    return (read == 1) ? EVOLUTION_SUCCESS : EVOLUTION_ERROR_IO;
}

// Error string conversion
const char* evolution_error_string(EvolutionError error) {
    switch (error) {
        case EVOLUTION_SUCCESS: return "Success";
        case EVOLUTION_ERROR_CONVERGENCE: return "Convergence error";
        case EVOLUTION_ERROR_TIMESTEP: return "Timestep error";
        case EVOLUTION_ERROR_QNM: return "QNM calculation error";
        case EVOLUTION_ERROR_IO: return "I/O error";
        case EVOLUTION_ERROR_CONFIG: return "Configuration error";
        case EVOLUTION_ERROR_MEMORY: return "Memory error";
        case EVOLUTION_ERROR_PHYSICS: return "Physics error";
        default: return "Unknown error";
    }
}
