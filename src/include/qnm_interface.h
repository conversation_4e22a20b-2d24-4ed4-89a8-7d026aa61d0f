#ifndef QNM_INTERFACE_H
#define QNM_INTERFACE_H

// Structure to hold QNM calculation results
typedef struct {
    double omega_real;    // Real part of frequency
    double omega_imag;    // Imaginary part of frequency  
    double lambda;        // Angular eigenvalue
    int converged;        // Convergence flag (1=converged, 0=failed)
} QNMResult;

// Function declarations
QNMResult calculate_qnm(double M, double a, double mu, int l, int m, int n);
int check_superradiance(double omega_real, double m_azimuthal, double omega_horizon);
double calculate_omega_horizon(double M, double a);
double estimate_superradiance_rate(QNMResult qnm, double M, double a, int m);

// Cache management
void clear_qnm_cache(void);
int get_cache_hits(void);

#endif // QNM_INTERFACE_H 