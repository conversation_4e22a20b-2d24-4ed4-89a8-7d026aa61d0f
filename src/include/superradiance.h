#ifndef SUPERRADIANCE_H
#define SUPERRADIANCE_H

#include "qnm_interface.h"

// Structure for superradiance rates
typedef struct {
    double mass_rate;         // dM/dt due to superradiance
    double spin_rate;         // da/dt due to superradiance
    double amplitude;         // Superradiant amplification factor
    double growth_timescale;  // Characteristic growth time
} SuperradianceRate;

// Structure for total superradiance from all modes
typedef struct {
    double total_mass_rate;   // Total dM/dt from all modes
    double total_spin_rate;   // Total da/dt from all modes
    int dominant_l;           // Dominant mode quantum number l
    int dominant_m;           // Dominant mode quantum number m
    int dominant_n;           // Dominant mode overtone n
    double dominant_timescale; // Growth time of dominant mode
} SuperradianceTotalRate;

// Function declarations
SuperradianceRate compute_superradiance_rate(
    double M, double a, double mu_scalar, 
    QNMResult qnm, int l, int m, double cloud_mass
);

SuperradianceTotalRate calculate_total_superradiance(
    double M, double a, double mu_scalar, 
    int l_max, int n_max, double cloud_mass
);

double estimate_critical_spin(double M, double mu_scalar, int l, int m);
int can_superradiate(double M, double a, double mu_scalar);

// Utility functions
double calculate_amplification_factor(
    double omega_real, double omega_imag, 
    double m_azimuthal, double omega_horizon
);

double estimate_cloud_mass(
    double M, double a, double mu_scalar, 
    double amplification, double initial_fraction
);

// Unit conversions
double GeV_to_kg(double m_GeV);
double kg_to_GeV(double m_kg);

#endif // SUPERRADIANCE_H 