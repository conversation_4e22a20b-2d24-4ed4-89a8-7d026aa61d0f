#ifndef EVOLUTION_H
#define EVOLUTION_H

#include "qnm_interface.h"
#include "superradiance.h"

// Evolution error codes
typedef enum {
    EVOLUTION_SUCCESS = 0,
    EVOLUTION_ERROR_CONVERGENCE,
    EVOLUTION_ERROR_TIMESTEP,
    EVOLUTION_ERROR_QNM,
    EVOLUTION_ERROR_IO,
    EVOLUTION_ERROR_CONFIG,
    EVOLUTION_ERROR_MEMORY,
    EVOLUTION_ERROR_PHYSICS
} EvolutionError;

// Black hole state structure
typedef struct {
    double M;                    // Mass (kg)
    double a;                    // Dimensionless spin parameter
    double Q;                    // Charge (optional, Coulombs)
    double t;                    // Current time (seconds)
    double dt;                   // Current timestep (seconds)
    
    // Scalar field cloud properties
    double mu_scalar;            // Scalar field mass (kg)
    double cloud_mass;           // Current cloud mass (kg)
    double cloud_energy;         // Cloud energy (Joules)
    
    // Evolution rates (per second)
    double dM_dt_hawking;        // Hawking mass rate
    double da_dt_hawking;        // Hawking spin rate
    double dM_dt_super;          // Superradiance mass rate
    double da_dt_super;          // Superradiance spin rate
    double dM_dt_total;          // Total mass rate
    double da_dt_total;          // Total spin rate
    
    // Diagnostics
    double total_energy_radiated;
    double total_angular_momentum_lost;
    double energy_conservation_error;
    int evolution_step;
    
    // Numerical state
    int converged;
    int stable;
} BlackHoleState;

// Configuration structure
typedef struct {
    // Evolution parameters
    double t_initial;            // Initial time (s)
    double t_final;              // Final time (s)
    double dt_initial;           // Initial timestep (s)
    double dt_min;               // Minimum timestep (s)
    double dt_max;               // Maximum timestep (s)
    double tolerance;            // Numerical tolerance
    
    // Physics parameters
    int enable_hawking;          // Enable Hawking radiation
    int enable_superradiance;    // Enable superradiance
    double scalar_mass;          // Scalar field mass (kg)
    int l_max;                   // Maximum l quantum number
    int m_max;                   // Maximum m quantum number  
    int n_max;                   // Maximum overtone number
    double initial_cloud_fraction; // Initial cloud mass fraction
    
    // Numerical parameters
    int adaptive_timestep;       // Use adaptive timestep
    int max_iterations;          // Maximum evolution steps
    double convergence_threshold; // Convergence criterion
    int integration_method;      // 0=Euler, 1=RK4, 2=adaptive
    
    // Output parameters
    char output_dir[256];        // Output directory
    int output_frequency;        // Output every N steps
    int verbose;                 // Verbosity level
    int save_intermediate;       // Save intermediate results
    
    // Cache parameters
    int enable_cache;            // Enable QNM caching
    int cache_size;              // Cache size
    char cache_file[256];        // Cache file path
} EvolutionConfig;

// Hawking radiation interface structure
typedef struct {
    void *blackhawk_data;        // BlackHawk internal data
    double (*calculate_mass_rate)(double M, double a, double Q);
    double (*calculate_spin_rate)(double M, double a, double Q);
    int (*initialize)(const char *param_file);
    void (*cleanup)(void);
} HawkingInterface;

// Function declarations

// Core evolution functions
EvolutionError evolve_black_hole(BlackHoleState *bh, EvolutionConfig *config);
EvolutionError evolution_step(BlackHoleState *bh, EvolutionConfig *config, 
                             HawkingInterface *hawking);

// State management
EvolutionError initialize_black_hole_state(BlackHoleState *bh, EvolutionConfig *config);
EvolutionError update_black_hole_state(BlackHoleState *bh, double dt);
void copy_black_hole_state(const BlackHoleState *src, BlackHoleState *dst);

// Configuration management
EvolutionError load_config(const char *filename, EvolutionConfig *config);
EvolutionError validate_config(const EvolutionConfig *config);
void set_default_config(EvolutionConfig *config);

// Time stepping
double calculate_adaptive_timestep(const BlackHoleState *bh, const EvolutionConfig *config);
double estimate_hawking_timescale(const BlackHoleState *bh);
double estimate_superradiance_timescale(const BlackHoleState *bh);
double estimate_numerical_stability_limit(const BlackHoleState *bh);

// Physics calculations
EvolutionError calculate_hawking_rates(const BlackHoleState *bh, 
                                      HawkingInterface *hawking,
                                      double *dM_dt, double *da_dt);
EvolutionError calculate_superradiance_rates(const BlackHoleState *bh,
                                           const EvolutionConfig *config,
                                           double *dM_dt, double *da_dt);

// Diagnostics
double calculate_energy_conservation_error(const BlackHoleState *bh);
int check_physical_consistency(const BlackHoleState *bh);
void update_diagnostics(BlackHoleState *bh, double dt);

// I/O functions
EvolutionError save_state(const BlackHoleState *bh, const char *filename);
EvolutionError load_state(BlackHoleState *bh, const char *filename);
EvolutionError write_evolution_data(const BlackHoleState *bh, const char *filename, int append);

// Utility functions
const char* evolution_error_string(EvolutionError error);
void print_black_hole_state(const BlackHoleState *bh);
void print_config(const EvolutionConfig *config);

// Integration methods
EvolutionError euler_step(BlackHoleState *bh, double dt, 
                         double dM_dt, double da_dt);
EvolutionError rk4_step(BlackHoleState *bh, double dt,
                       EvolutionConfig *config, HawkingInterface *hawking);

#endif // EVOLUTION_H
