// Superradiance calculation module
// Computes mass and angular momentum extraction rates

#include <stdio.h>
#include <stdlib.h>
#include <math.h>
#include "superradiance.h"
#include "qnm_interface.h"

#define PI 3.14159265358979323846
#define G_NEWTON 6.67430e-11  // m^3 kg^-1 s^-2
#define C_LIGHT 2.99792458e8   // m/s
#define HBAR 1.054571817e-34   // J⋅s

// Convert between units
double GeV_to_kg(double m_GeV) {
    return m_GeV * 1.78266192e-27;
}

double kg_to_GeV(double m_kg) {
    return m_kg / 1.78266192e-27;
}

// Calculate the superradiant amplification factor
double calculate_amplification_factor(double omega_real, double omega_imag, 
                                    double m_azimuthal, double omega_horizon) {
    double delta_omega = omega_real - m_azimuthal * omega_horizon;
    if (delta_omega < 0 && omega_imag > 0) {
        // Superradiant regime
        return exp(2.0 * PI * omega_imag / fabs(delta_omega));
    }
    return 1.0;
}

// Calculate occupation number for bosonic field
double calculate_occupation_number(double omega, double temperature) {
    if (temperature <= 0) return 0.0;
    double x = omega / temperature;
    if (x > 700) return 0.0;  // Avoid overflow
    return 1.0 / (exp(x) - 1.0);
}

// Estimate scalar field cloud mass
double estimate_cloud_mass(double M, double a, double mu_scalar, 
                          double amplification, double initial_fraction) {
    // Cloud mass grows exponentially during superradiance
    // M_cloud ~ M * initial_fraction * exp(amplification * t/t_growth)
    // This is a simplified estimate
    double gravitational_coupling = G_NEWTON * M * mu_scalar / (HBAR * C_LIGHT);
    double max_cloud_fraction = 0.1;  // Maximum ~10% of BH mass
    
    double cloud_fraction = initial_fraction * amplification;
    if (cloud_fraction > max_cloud_fraction) {
        cloud_fraction = max_cloud_fraction;
    }
    
    return M * cloud_fraction;
}

// Calculate energy extraction rate
double calculate_energy_extraction_rate(QNMResult qnm, double M, double a, 
                                       double mu_scalar, double cloud_mass) {
    if (qnm.omega_imag <= 0) return 0.0;  // No superradiance
    
    // Energy extraction rate: dE/dt = 2 * omega_I * E_cloud
    // where E_cloud ~ mu * M_cloud * c^2
    double energy_cloud = mu_scalar * cloud_mass;
    double extraction_rate = 2.0 * qnm.omega_imag * energy_cloud;
    
    return extraction_rate;
}

// Calculate angular momentum extraction rate  
double calculate_angular_momentum_rate(double energy_rate, double omega_real, 
                                      int m_azimuthal) {
    if (omega_real <= 0) return 0.0;
    return m_azimuthal * energy_rate / omega_real;
}

// Main function to compute superradiance rates
SuperradianceRate compute_superradiance_rate(double M, double a, double mu_scalar,
                                            QNMResult qnm, int l, int m,
                                            double cloud_mass) {
    SuperradianceRate rate = {0.0, 0.0, 0.0, 0.0};
    
    // Check if in superradiant regime
    double omega_horizon = calculate_omega_horizon(M, a);
    if (!check_superradiance(qnm.omega_real, m, omega_horizon)) {
        return rate;  // Not superradiant
    }
    
    // Calculate rates
    double amplification = calculate_amplification_factor(
        qnm.omega_real, qnm.omega_imag, m, omega_horizon);
    
    double energy_rate = calculate_energy_extraction_rate(
        qnm, M, a, mu_scalar, cloud_mass);
    
    double angular_momentum_rate = calculate_angular_momentum_rate(
        energy_rate, qnm.omega_real, m);
    
    // Convert to mass and spin change rates
    rate.mass_rate = -energy_rate / (C_LIGHT * C_LIGHT);  // dM/dt < 0
    
    // da/dt = (dL/dt - 2*a*M*dM/dt) / M^2
    double L = a * M * M * G_NEWTON / C_LIGHT;  // Angular momentum
    double dL_dt = -angular_momentum_rate;
    rate.spin_rate = (dL_dt - 2.0*a*M*rate.mass_rate) / (M*M);
    
    rate.amplitude = amplification;
    rate.growth_timescale = 1.0 / (2.0 * qnm.omega_imag);
    
    return rate;
}

// Calculate all relevant modes and sum contributions
SuperradianceTotalRate calculate_total_superradiance(
    double M, double a, double mu_scalar, 
    int l_max, int n_max, double cloud_mass) {
    
    SuperradianceTotalRate total = {0.0, 0.0, 0, 0, 0, 0.0};
    
    // Loop over quantum numbers
    for (int l = 1; l <= l_max; l++) {
        for (int m = -l; m <= l; m++) {
            if (m <= 0) continue;  // Only positive m for superradiance
            
            for (int n = 0; n <= n_max; n++) {
                // Calculate QNM for this mode
                QNMResult qnm = calculate_qnm(M, a, mu_scalar, l, m, n);
                
                if (!qnm.converged) continue;
                
                // Check if superradiant
                double omega_horizon = calculate_omega_horizon(M, a);
                if (!check_superradiance(qnm.omega_real, m, omega_horizon)) {
                    continue;
                }
                
                // Calculate rate for this mode
                SuperradianceRate mode_rate = compute_superradiance_rate(
                    M, a, mu_scalar, qnm, l, m, cloud_mass);
                
                // Add to total (assuming modes are independent)
                total.total_mass_rate += mode_rate.mass_rate;
                total.total_spin_rate += mode_rate.spin_rate;
                
                // Track dominant mode
                if (mode_rate.growth_timescale > 0 && 
                    (total.dominant_timescale == 0 || 
                     mode_rate.growth_timescale < total.dominant_timescale)) {
                    total.dominant_l = l;
                    total.dominant_m = m;
                    total.dominant_n = n;
                    total.dominant_timescale = mode_rate.growth_timescale;
                }
            }
        }
    }
    
    return total;
}

// Estimate critical spin below which superradiance stops
double estimate_critical_spin(double M, double mu_scalar, int l, int m) {
    // For the hydrogenic approximation: a_crit ≈ 4*m*mu*M
    // This is a rough estimate, actual value requires solving QNM equations
    double alpha = G_NEWTON * M * mu_scalar / (HBAR * C_LIGHT);
    return 4.0 * m * alpha;
}

// Check if black hole parameters allow superradiance
int can_superradiate(double M, double a, double mu_scalar) {
    // Basic checks
    if (a <= 0) return 0;  // Need rotation
    if (mu_scalar <= 0) return 0;  // Need massive field
    
    // Check if any mode can be superradiant
    // Rough criterion: need a > 4*mu*M for m=1 mode
    double a_crit_min = estimate_critical_spin(M, mu_scalar, 1, 1);
    
    return (a > a_crit_min);
} 