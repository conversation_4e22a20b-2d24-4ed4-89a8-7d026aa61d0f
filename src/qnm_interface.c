// QNM calculation interface for C-Julia integration
// This interfaces with the Isomonodromic Method Julia code

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>
#include "qnm_interface.h"

// Cache structure for QNM results
typedef struct {
    double a;
    double mu;
    int l;
    int m;
    QNMResult result;
} QNMCache;

#define CACHE_SIZE 1000
static QNMCache qnm_cache[CACHE_SIZE];
static int cache_count = 0;

// Check if QNM result is in cache
QNMResult* check_cache(double a, double mu, int l, int m) {
    for (int i = 0; i < cache_count; i++) {
        if (fabs(qnm_cache[i].a - a) < 1e-10 &&
            fabs(qnm_cache[i].mu - mu) < 1e-10 &&
            qnm_cache[i].l == l &&
            qnm_cache[i].m == m) {
            return &(qnm_cache[i].result);
        }
    }
    return NULL;
}

// Add result to cache
void add_to_cache(double a, double mu, int l, int m, QNMResult result) {
    if (cache_count < CACHE_SIZE) {
        qnm_cache[cache_count].a = a;
        qnm_cache[cache_count].mu = mu;
        qnm_cache[cache_count].l = l;
        qnm_cache[cache_count].m = m;
        qnm_cache[cache_count].result = result;
        cache_count++;
    }
}

// Call Julia QNM calculator via system call
QNMResult call_julia_qnm(double M, double a, double mu, int l, int m, int n) {
    QNMResult result = {0.0, 0.0, 0.0, 0};
    
    // Check cache first
    QNMResult* cached = check_cache(a/M, mu*M, l, m);
    if (cached != NULL) {
        result = *cached;
        result.omega_real /= M;  // Convert back to physical units
        result.omega_imag /= M;
        return result;
    }
    
    // Prepare Julia script call
    char command[1024];
    char output_file[256];
    sprintf(output_file, "/tmp/qnm_result_%d.txt", getpid());
    
    // Create Julia wrapper script
    FILE *wrapper = fopen("/tmp/qnm_wrapper.jl", "w");
    if (!wrapper) {
        printf("Error: Cannot create wrapper script\n");
        return result;
    }
    
    fprintf(wrapper, 
        "include(\"%s/external/Isomonodromic Method/Script - Julia/Massive_QNMs_Kerr_BH.jl\")\n"
        "a_M = %f\n"
        "M_mu = %f\n"
        "angularl = %d\n"
        "angularm = %d\n"
        "overtone = %d\n"
        "eta = acos(a_M)\n"
        "mu = M_mu\n"
        "# Call QNM calculation\n"
        "result = calculate_qnm_for_params(a_M, M_mu, angularl, angularm, overtone)\n"
        "# Write result\n"
        "open(\"%s\", \"w\") do f\n"
        "    println(f, real(result[1]), \" \", imag(result[1]), \" \", real(result[2]))\n"
        "end\n",
        getenv("PWD"), a/M, mu*M, l, m, n, output_file
    );
    fclose(wrapper);
    
    // Execute Julia script
    sprintf(command, "julia /tmp/qnm_wrapper.jl > /dev/null 2>&1");
    int status = system(command);
    
    if (status == 0) {
        // Read results
        FILE *output = fopen(output_file, "r");
        if (output) {
            fscanf(output, "%lf %lf %lf", 
                   &result.omega_real, &result.omega_imag, &result.lambda);
            result.converged = 1;
            fclose(output);
            
            // Add to cache (in dimensionless units)
            QNMResult cached_result = result;
            cached_result.omega_real *= M;
            cached_result.omega_imag *= M;
            add_to_cache(a/M, mu*M, l, m, cached_result);
            
            // Convert to physical units
            result.omega_real /= M;
            result.omega_imag /= M;
        }
    }
    
    // Cleanup
    remove(output_file);
    remove("/tmp/qnm_wrapper.jl");
    
    return result;
}

// Calculate QNM for given parameters
QNMResult calculate_qnm(double M, double a, double mu, int l, int m, int n) {
    return call_julia_qnm(M, a, mu, l, m, n);
}

// Check if mode is superradiant
int check_superradiance(double omega_real, double m_azimuthal, double omega_horizon) {
    return (omega_real < m_azimuthal * omega_horizon) && (omega_real > 0);
}

// Calculate horizon angular velocity
double calculate_omega_horizon(double M, double a) {
    double rplus = M * (1.0 + sqrt(1.0 - (a/M)*(a/M)));
    return a / (2.0 * M * rplus);
}

// Estimate superradiance growth rate from QNM
double estimate_superradiance_rate(QNMResult qnm, double M, double a, int m) {
    if (qnm.omega_imag > 0) {
        // Growing mode - superradiance
        return 2.0 * qnm.omega_imag;  // Factor of 2 from e^{2*omega_I*t}
    }
    return 0.0;
} 