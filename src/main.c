// Main program for unified black hole evolution
// Combines Hawking radiation and superradiance effects

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <signal.h>
#include <sys/stat.h>
#include "evolution.h"

// Forward declarations for functions from config.c
EvolutionError load_config(const char* filename, EvolutionConfig* config);
EvolutionError create_output_directory(const char* dir_path);
void print_config(const EvolutionConfig* config);
void set_default_config(EvolutionConfig* config);

// Global variables for signal handling
static volatile int interrupted = 0;
static BlackHoleState* global_bh = NULL;
static EvolutionConfig* global_config = NULL;

// Signal handler for graceful shutdown
void signal_handler(int sig) {
    printf("\nReceived signal %d, saving state and exiting...\n", sig);
    interrupted = 1;
    
    if (global_bh && global_config) {
        char filename[512];
        snprintf(filename, sizeof(filename), "%s/interrupted_state.dat", 
                global_config->output_dir);
        save_state(global_bh, filename);
        printf("State saved to %s\n", filename);
    }
}

// Print usage information
void print_usage(const char* program_name) {
    printf("Usage: %s [options]\n", program_name);
    printf("Options:\n");
    printf("  -c, --config FILE     Configuration file (default: parameters.txt)\n");
    printf("  -M, --mass MASS       Initial black hole mass in kg\n");
    printf("  -a, --spin SPIN       Initial dimensionless spin parameter\n");
    printf("  -o, --output DIR      Output directory (default: ./results)\n");
    printf("  -v, --verbose LEVEL   Verbosity level (0-3, default: 1)\n");
    printf("  -h, --help            Show this help message\n");
    printf("\nExample:\n");
    printf("  %s -M 1e30 -a 0.7 -c my_config.txt\n", program_name);
}

// Parse command line arguments
EvolutionError parse_arguments(int argc, char* argv[], EvolutionConfig* config, 
                              BlackHoleState* bh) {
    if (!config || !bh) return EVOLUTION_ERROR_CONFIG;
    
    // Set defaults
    char config_file[256] = "parameters.txt";
    bh->M = 1e30;  // 1e30 kg ~ 500 solar masses
    bh->a = 0.5;   // Moderate spin
    bh->Q = 0.0;   // Uncharged
    
    for (int i = 1; i < argc; i++) {
        if (strcmp(argv[i], "-c") == 0 || strcmp(argv[i], "--config") == 0) {
            if (i + 1 < argc) {
                strcpy(config_file, argv[++i]);
            } else {
                printf("Error: --config requires a filename\n");
                return EVOLUTION_ERROR_CONFIG;
            }
        }
        else if (strcmp(argv[i], "-M") == 0 || strcmp(argv[i], "--mass") == 0) {
            if (i + 1 < argc) {
                bh->M = atof(argv[++i]);
                if (bh->M <= 0) {
                    printf("Error: Mass must be positive\n");
                    return EVOLUTION_ERROR_CONFIG;
                }
            } else {
                printf("Error: --mass requires a value\n");
                return EVOLUTION_ERROR_CONFIG;
            }
        }
        else if (strcmp(argv[i], "-a") == 0 || strcmp(argv[i], "--spin") == 0) {
            if (i + 1 < argc) {
                bh->a = atof(argv[++i]);
                if (bh->a < 0 || bh->a >= 1) {
                    printf("Error: Spin parameter must be in range [0, 1)\n");
                    return EVOLUTION_ERROR_CONFIG;
                }
            } else {
                printf("Error: --spin requires a value\n");
                return EVOLUTION_ERROR_CONFIG;
            }
        }
        else if (strcmp(argv[i], "-o") == 0 || strcmp(argv[i], "--output") == 0) {
            if (i + 1 < argc) {
                strcpy(config->output_dir, argv[++i]);
            } else {
                printf("Error: --output requires a directory\n");
                return EVOLUTION_ERROR_CONFIG;
            }
        }
        else if (strcmp(argv[i], "-v") == 0 || strcmp(argv[i], "--verbose") == 0) {
            if (i + 1 < argc) {
                config->verbose = atoi(argv[++i]);
                if (config->verbose < 0 || config->verbose > 3) {
                    printf("Error: Verbosity level must be 0-3\n");
                    return EVOLUTION_ERROR_CONFIG;
                }
            } else {
                printf("Error: --verbose requires a level\n");
                return EVOLUTION_ERROR_CONFIG;
            }
        }
        else if (strcmp(argv[i], "-h") == 0 || strcmp(argv[i], "--help") == 0) {
            print_usage(argv[0]);
            exit(0);
        }
        else {
            printf("Error: Unknown option '%s'\n", argv[i]);
            print_usage(argv[0]);
            return EVOLUTION_ERROR_CONFIG;
        }
    }
    
    // Load configuration file
    return load_config(config_file, config);
}

// Print black hole state
void print_black_hole_state(const BlackHoleState* bh) {
    if (!bh) return;
    
    printf("Black Hole State (Step %d):\n", bh->evolution_step);
    printf("  Time: %.3e s (%.3e years)\n", bh->t, bh->t / (365.25 * 24 * 3600));
    printf("  Mass: %.3e kg (%.3f M_sun)\n", bh->M, bh->M / 1.989e30);
    printf("  Spin: %.6f\n", bh->a);
    printf("  Cloud mass: %.3e kg (%.3e M_BH)\n", bh->cloud_mass, bh->cloud_mass / bh->M);
    printf("  Timestep: %.3e s (%.3e years)\n", bh->dt, bh->dt / (365.25 * 24 * 3600));
    printf("  Rates:\n");
    printf("    dM/dt (Hawking): %.3e kg/s\n", bh->dM_dt_hawking);
    printf("    dM/dt (Super):   %.3e kg/s\n", bh->dM_dt_super);
    printf("    da/dt (Hawking): %.3e /s\n", bh->da_dt_hawking);
    printf("    da/dt (Super):   %.3e /s\n", bh->da_dt_super);
    printf("  Energy conservation error: %.3e\n", bh->energy_conservation_error);
    printf("\n");
}

// Main evolution loop
EvolutionError evolve_black_hole(BlackHoleState* bh, EvolutionConfig* config) {
    if (!bh || !config) return EVOLUTION_ERROR_CONFIG;
    
    EvolutionError error;
    HawkingInterface hawking = {0}; // Placeholder
    
    // Initialize
    error = initialize_black_hole_state(bh, config);
    if (error != EVOLUTION_SUCCESS) return error;
    
    // Create output directory
    error = create_output_directory(config->output_dir);
    if (error != EVOLUTION_SUCCESS) return error;
    
    // Open output files
    char evolution_file[512];
    snprintf(evolution_file, sizeof(evolution_file), "%s/evolution.dat", config->output_dir);
    FILE* output = fopen(evolution_file, "w");
    if (!output) {
        printf("Error: Could not open output file %s\n", evolution_file);
        return EVOLUTION_ERROR_IO;
    }
    
    // Write header
    fprintf(output, "# Time(s) Mass(kg) Spin CloudMass(kg) dM_dt_H(kg/s) dM_dt_S(kg/s) da_dt_H(/s) da_dt_S(/s) dt(s)\n");
    
    if (config->verbose > 0) {
        printf("Starting black hole evolution...\n");
        print_black_hole_state(bh);
    }
    
    // Main evolution loop
    clock_t start_time = clock();
    int output_counter = 0;
    
    while (bh->t < config->t_final && bh->evolution_step < config->max_iterations && !interrupted) {
        // Evolution step
        error = evolution_step(bh, config, &hawking);
        if (error != EVOLUTION_SUCCESS) {
            printf("Error in evolution step: %s\n", evolution_error_string(error));
            break;
        }
        
        // Check for completion
        if (bh->M <= 0) {
            if (config->verbose > 0) {
                printf("Black hole has evaporated completely.\n");
            }
            break;
        }
        
        // Output
        if (bh->evolution_step % config->output_frequency == 0) {
            fprintf(output, "%.6e %.6e %.6f %.6e %.6e %.6e %.6e %.6e %.6e\n",
                   bh->t, bh->M, bh->a, bh->cloud_mass,
                   bh->dM_dt_hawking, bh->dM_dt_super,
                   bh->da_dt_hawking, bh->da_dt_super, bh->dt);
            fflush(output);
            
            if (config->verbose > 1) {
                print_black_hole_state(bh);
            } else if (config->verbose > 0 && output_counter % 10 == 0) {
                printf("Step %d: t=%.3e s, M=%.3e kg, a=%.3f\n", 
                       bh->evolution_step, bh->t, bh->M, bh->a);
            }
            output_counter++;
        }
        
        // Save intermediate state
        if (config->save_intermediate && bh->evolution_step % (config->output_frequency * 10) == 0) {
            char state_file[512];
            snprintf(state_file, sizeof(state_file), "%s/state_%d.dat", 
                    config->output_dir, bh->evolution_step);
            save_state(bh, state_file);
        }
    }
    
    fclose(output);
    
    // Final output
    clock_t end_time = clock();
    double cpu_time = ((double)(end_time - start_time)) / CLOCKS_PER_SEC;
    
    if (config->verbose > 0) {
        printf("\nEvolution completed!\n");
        printf("Final state:\n");
        print_black_hole_state(bh);
        printf("CPU time: %.3f seconds\n", cpu_time);
        printf("Results saved to %s\n", config->output_dir);
    }
    
    return EVOLUTION_SUCCESS;
}

// Main function
int main(int argc, char* argv[]) {
    printf("Unified Black Hole Evolution Code\n");
    printf("=================================\n\n");
    
    // Set up signal handling
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    // Initialize structures
    EvolutionConfig config;
    BlackHoleState bh;
    set_default_config(&config);
    
    // Set global pointers for signal handler
    global_config = &config;
    global_bh = &bh;
    
    // Parse arguments and load config
    EvolutionError error = parse_arguments(argc, argv, &config, &bh);
    if (error != EVOLUTION_SUCCESS) {
        printf("Error: %s\n", evolution_error_string(error));
        return 1;
    }
    
    // Print configuration
    if (config.verbose > 0) {
        print_config(&config);
    }
    
    // Run evolution
    error = evolve_black_hole(&bh, &config);
    if (error != EVOLUTION_SUCCESS) {
        printf("Evolution failed: %s\n", evolution_error_string(error));
        return 1;
    }
    
    return 0;
}
