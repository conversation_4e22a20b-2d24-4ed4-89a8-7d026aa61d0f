// Test program for the unified evolution system
// Tests the main evolution driver and configuration system

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>
#include <assert.h>
#include "evolution.h"

// Forward declarations
EvolutionError load_config(const char* filename, EvolutionConfig* config);
void set_default_config(EvolutionConfig* config);
void print_config(const EvolutionConfig* config);
EvolutionError create_output_directory(const char* dir_path);

// Test configuration system
int test_config_system() {
    printf("Testing configuration system...\n");
    
    EvolutionConfig config;
    
    // Test default configuration
    set_default_config(&config);
    EvolutionError error = validate_config(&config);
    if (error != EVOLUTION_SUCCESS) {
        printf("ERROR: Default configuration is invalid: %s\n", 
               evolution_error_string(error));
        return 0;
    }
    printf("✓ Default configuration is valid\n");
    
    // Test configuration loading (will use defaults if file doesn't exist)
    error = load_config("test_parameters.txt", &config);
    if (error != EVOLUTION_SUCCESS) {
        printf("ERROR: Failed to load configuration: %s\n", 
               evolution_error_string(error));
        return 0;
    }
    printf("✓ Configuration loading works\n");
    
    // Test invalid configurations
    config.t_final = config.t_initial - 1.0; // Invalid time range
    error = validate_config(&config);
    if (error == EVOLUTION_SUCCESS) {
        printf("ERROR: Invalid configuration was accepted\n");
        return 0;
    }
    printf("✓ Invalid configuration properly rejected\n");
    
    return 1;
}

// Test black hole state initialization
int test_state_initialization() {
    printf("Testing black hole state initialization...\n");
    
    EvolutionConfig config;
    BlackHoleState bh;
    
    set_default_config(&config);
    
    // Set initial conditions
    bh.M = 1e30;  // 500 solar masses
    bh.a = 0.7;   // High spin
    bh.Q = 0.0;   // Uncharged
    
    EvolutionError error = initialize_black_hole_state(&bh, &config);
    if (error != EVOLUTION_SUCCESS) {
        printf("ERROR: Failed to initialize black hole state: %s\n", 
               evolution_error_string(error));
        return 0;
    }
    
    // Check initialization
    if (bh.t != config.t_initial) {
        printf("ERROR: Time not initialized correctly\n");
        return 0;
    }
    
    if (bh.cloud_mass <= 0 || bh.cloud_mass > bh.M) {
        printf("ERROR: Cloud mass not initialized correctly\n");
        return 0;
    }
    
    if (bh.evolution_step != 0) {
        printf("ERROR: Evolution step not initialized correctly\n");
        return 0;
    }
    
    printf("✓ Black hole state initialization works\n");
    return 1;
}

// Test timescale calculations
int test_timescale_calculations() {
    printf("Testing timescale calculations...\n");
    
    BlackHoleState bh;
    bh.M = 1e30;  // 500 solar masses
    bh.a = 0.7;
    bh.mu_scalar = 1e-12 * 1.78266192e-27; // 1e-12 eV in kg
    bh.dM_dt_total = -1e20; // Some mass loss rate
    bh.da_dt_total = -1e-10; // Some spin loss rate
    
    double t_hawking = estimate_hawking_timescale(&bh);
    double t_super = estimate_superradiance_timescale(&bh);
    double t_stability = estimate_numerical_stability_limit(&bh);
    
    if (t_hawking <= 0 || t_super <= 0 || t_stability <= 0) {
        printf("ERROR: Timescales must be positive\n");
        return 0;
    }
    
    printf("  Hawking timescale: %.3e s (%.3e years)\n", 
           t_hawking, t_hawking / (365.25 * 24 * 3600));
    printf("  Superradiance timescale: %.3e s (%.3e years)\n", 
           t_super, t_super / (365.25 * 24 * 3600));
    printf("  Stability timescale: %.3e s (%.3e years)\n", 
           t_stability, t_stability / (365.25 * 24 * 3600));
    
    printf("✓ Timescale calculations work\n");
    return 1;
}

// Test adaptive timestep
int test_adaptive_timestep() {
    printf("Testing adaptive timestep...\n");
    
    EvolutionConfig config;
    BlackHoleState bh;
    
    set_default_config(&config);
    bh.M = 1e30;
    bh.a = 0.7;
    bh.dt = config.dt_initial;
    bh.dM_dt_total = -1e20;
    bh.da_dt_total = -1e-10;
    
    double dt_new = calculate_adaptive_timestep(&bh, &config);
    
    if (dt_new < config.dt_min || dt_new > config.dt_max) {
        printf("ERROR: Adaptive timestep outside allowed range\n");
        return 0;
    }
    
    printf("  Initial timestep: %.3e s\n", bh.dt);
    printf("  Adaptive timestep: %.3e s\n", dt_new);
    
    printf("✓ Adaptive timestep calculation works\n");
    return 1;
}

// Test superradiance condition checking
int test_superradiance_conditions() {
    printf("Testing superradiance conditions...\n");
    
    double M = 1e30;
    double mu = 1e-12 * 1.78266192e-27; // 1e-12 eV in kg
    
    // Test non-rotating black hole (should not superradiate)
    int can_super = can_superradiate(M, 0.0, mu);
    if (can_super) {
        printf("ERROR: Non-rotating black hole should not superradiate\n");
        return 0;
    }
    
    // Test rotating black hole with massive field (should superradiate)
    can_super = can_superradiate(M, 0.7, mu);
    if (!can_super) {
        printf("WARNING: Rotating black hole with massive field should superradiate\n");
        // This might be OK depending on the specific parameters
    }
    
    // Test massless field (should not superradiate)
    can_super = can_superradiate(M, 0.7, 0.0);
    if (can_super) {
        printf("ERROR: Massless field should not superradiate\n");
        return 0;
    }
    
    printf("✓ Superradiance condition checking works\n");
    return 1;
}

// Test I/O functions
int test_io_functions() {
    printf("Testing I/O functions...\n");
    
    BlackHoleState bh_original, bh_loaded;
    
    // Initialize original state
    bh_original.M = 1e30;
    bh_original.a = 0.7;
    bh_original.t = 1e10;
    bh_original.evolution_step = 1000;
    
    // Save state
    EvolutionError error = save_state(&bh_original, "test_state.dat");
    if (error != EVOLUTION_SUCCESS) {
        printf("ERROR: Failed to save state: %s\n", evolution_error_string(error));
        return 0;
    }
    
    // Load state
    error = load_state(&bh_loaded, "test_state.dat");
    if (error != EVOLUTION_SUCCESS) {
        printf("ERROR: Failed to load state: %s\n", evolution_error_string(error));
        return 0;
    }
    
    // Compare states
    if (fabs(bh_loaded.M - bh_original.M) > 1e-10 ||
        fabs(bh_loaded.a - bh_original.a) > 1e-10 ||
        fabs(bh_loaded.t - bh_original.t) > 1e-10 ||
        bh_loaded.evolution_step != bh_original.evolution_step) {
        printf("ERROR: Loaded state does not match original\n");
        return 0;
    }
    
    // Cleanup
    remove("test_state.dat");
    
    printf("✓ I/O functions work\n");
    return 1;
}

// Test short evolution run
int test_short_evolution() {
    printf("Testing short evolution run...\n");
    
    EvolutionConfig config;
    BlackHoleState bh;
    
    set_default_config(&config);
    
    // Set up for short test run
    config.t_final = config.t_initial + 10 * config.dt_initial;
    config.max_iterations = 10;
    config.output_frequency = 1;
    config.verbose = 0; // Quiet for test
    strcpy(config.output_dir, "./test_results");
    
    // Initial conditions
    bh.M = 1e30;
    bh.a = 0.7;
    bh.Q = 0.0;
    
    // Create output directory
    EvolutionError error = create_output_directory(config.output_dir);
    if (error != EVOLUTION_SUCCESS) {
        printf("ERROR: Failed to create output directory: %s\n", 
               evolution_error_string(error));
        return 0;
    }
    
    // Initialize
    error = initialize_black_hole_state(&bh, &config);
    if (error != EVOLUTION_SUCCESS) {
        printf("ERROR: Failed to initialize: %s\n", evolution_error_string(error));
        return 0;
    }
    
    // Run a few evolution steps
    HawkingInterface hawking = {0}; // Placeholder
    
    for (int i = 0; i < 5; i++) {
        error = evolution_step(&bh, &config, &hawking);
        if (error != EVOLUTION_SUCCESS) {
            printf("ERROR: Evolution step failed: %s\n", evolution_error_string(error));
            return 0;
        }
        
        if (bh.M <= 0) {
            printf("ERROR: Black hole mass became non-positive\n");
            return 0;
        }
    }
    
    printf("  Completed %d evolution steps\n", bh.evolution_step);
    printf("  Final mass: %.3e kg\n", bh.M);
    printf("  Final spin: %.6f\n", bh.a);
    
    printf("✓ Short evolution run works\n");
    return 1;
}

// Main test function
int main() {
    printf("Unified Black Hole Evolution - Test Suite\n");
    printf("==========================================\n\n");
    
    int tests_passed = 0;
    int total_tests = 7;
    
    if (test_config_system()) tests_passed++;
    if (test_state_initialization()) tests_passed++;
    if (test_timescale_calculations()) tests_passed++;
    if (test_adaptive_timestep()) tests_passed++;
    if (test_superradiance_conditions()) tests_passed++;
    if (test_io_functions()) tests_passed++;
    if (test_short_evolution()) tests_passed++;
    
    printf("\nTest Results: %d/%d tests passed\n", tests_passed, total_tests);
    
    if (tests_passed == total_tests) {
        printf("✓ All tests passed! The system is ready for use.\n");
        return 0;
    } else {
        printf("✗ Some tests failed. Please check the implementation.\n");
        return 1;
    }
}
