# Makefile for unified BH evolution code
CC = gcc
CFLAGS = -Wall -O3 -g -I./include -I../external/blackhawk_v2.3/src
LDFLAGS = -lm

# Source files
SRCS = qnm_interface.c superradiance.c evolution.c config.c
OBJS = $(SRCS:.c=.o)

# BlackHawk source files to include
BH_SRCS = ../external/blackhawk_v2.3/src/evolution.c \
          ../external/blackhawk_v2.3/src/general.c \
          ../external/blackhawk_v2.3/src/primary.c \
          ../external/blackhawk_v2.3/src/spectrum.c \
          ../external/blackhawk_v2.3/src/technical.c
BH_OBJS = $(BH_SRCS:.c=.o)

# Target executable
TARGET = unified_evolution

# Default target
all: $(TARGET)

# Build the executable
$(TARGET): $(OBJS) $(BH_OBJS) main.o
	$(CC) $(CFLAGS) -o $@ $^ $(LDFLAGS)

# Compile source files
%.o: %.c
	$(CC) $(CFLAGS) -c $< -o $@

# Compile BlackHawk files with special flags
../external/blackhawk_v2.3/src/%.o: ../external/blackhawk_v2.3/src/%.c
	$(CC) $(CFLAGS) -c $< -o $@

# Test programs
test_qnm: test_qnm.o qnm_interface.o
	$(CC) $(CFLAGS) -o $@ $^ $(LDFLAGS)

test_super: test_superradiance.o superradiance.o qnm_interface.o
	$(CC) $(CFLAGS) -o $@ $^ $(LDFLAGS)

test_evolution: test_evolution.o $(OBJS)
	$(CC) $(CFLAGS) -o $@ $^ $(LDFLAGS)

# Main program (without BlackHawk for now)
unified_evolution_standalone: main.o $(OBJS)
	$(CC) $(CFLAGS) -o $@ $^ $(LDFLAGS)

# Clean
clean:
	rm -f $(OBJS) $(BH_OBJS) main.o test_qnm.o test_superradiance.o test_evolution.o
	rm -f $(TARGET) test_qnm test_super test_evolution unified_evolution_standalone
	rm -rf ./test_results ./results

# Install (optional)
install: $(TARGET)
	cp $(TARGET) ../bin/

.PHONY: all clean install 