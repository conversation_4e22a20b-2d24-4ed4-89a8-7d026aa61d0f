#!/usr/bin/env julia
# QNM wrapper for command-line interface
# This wraps the Isomonodromic Method code for easy C integration

# Add the path to the Isomonodromic Method
push!(LOAD_PATH, joinpath(@__DIR__, "../external/Isomonodromic Method/Script - Julia"))

# Modified version of the original code with a callable function
include("../external/Isomonodromic Method/Script - Julia/Massive_QNMs_Kerr_BH.jl")

# Function to calculate QNM for given parameters
function calculate_qnm_for_params(a_M::Float64, M_mu::Float64, l::Int, m::Int, n::Int)
    # Set global parameters
    global eta = acos(a_M)
    global mu = M_mu
    global angularl = l
    global angularm = m
    global spin = 0.5  # For scalar field
    
    # Initial guess (can be improved with better estimates)
    initial = zeros(ArbComplex, 2)
    if M_mu < 0.1
        initial[1] = ArbComplex(0.3 - 0.1im)
        initial[2] = ArbComplex(2.0)
    else
        initial[1] = ArbComplex(0.5 * M_mu - 0.05im)
        initial[2] = ArbComplex(l * (l + 1))
    end
    
    try
        # Call the Newton solver
        solution = newton2d(fr_wrapper, initial, false)
        
        # Return omega and lambda
        return (solution[1][1], solution[1][2])
    catch e
        # Return NaN if convergence fails
        return (ArbComplex(NaN, NaN), ArbComplex(NaN))
    end
end

# Main function for command-line usage
function main()
    if length(ARGS) < 5
        println("Usage: julia qnm_wrapper.jl a_M M_mu l m n")
        exit(1)
    end
    
    # Parse arguments
    a_M = parse(Float64, ARGS[1])
    M_mu = parse(Float64, ARGS[2])
    l = parse(Int, ARGS[3])
    m = parse(Int, ARGS[4])
    n = parse(Int, ARGS[5])
    
    # Calculate QNM
    omega, lambda = calculate_qnm_for_params(a_M, M_mu, l, m, n)
    
    # Output results (real and imaginary parts)
    println(real(omega), " ", imag(omega), " ", real(lambda))
end

# Run if called directly
if abspath(PROGRAM_FILE) == @__FILE__
    main()
end 