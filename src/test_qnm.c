// Test program for QNM interface
#include <stdio.h>
#include <stdlib.h>
#include <math.h>
#include "qnm_interface.h"

int main(int argc, char *argv[]) {
    printf("QNM Interface Test Program\n");
    printf("========================\n\n");
    
    // Test parameters
    double M = 1.0;        // BH mass (in some units)
    double a = 0.5;        // BH spin parameter
    double mu = 0.1;       // Scalar field mass
    int l = 1;             // Orbital quantum number
    int m = 1;             // Azimuthal quantum number
    int n = 0;             // Overtone number
    
    printf("Test parameters:\n");
    printf("  M = %.3f\n", M);
    printf("  a/M = %.3f\n", a/M);
    printf("  μM = %.3f\n", mu*M);
    printf("  (l,m,n) = (%d,%d,%d)\n\n", l, m, n);
    
    // Calculate QNM
    printf("Calculating QNM...\n");
    QNMResult qnm = calculate_qnm(M, a, mu, l, m, n);
    
    if (qnm.converged) {
        printf("\nQNM Result:\n");
        printf("  ω = %.6f + %.6fi\n", qnm.omega_real, qnm.omega_imag);
        printf("  λ = %.6f\n", qnm.lambda);
        
        // Check superradiance
        double omega_horizon = calculate_omega_horizon(M, a);
        printf("\n  Ω_H = %.6f\n", omega_horizon);
        printf("  mΩ_H = %.6f\n", m * omega_horizon);
        
        if (check_superradiance(qnm.omega_real, m, omega_horizon)) {
            printf("\n  *** SUPERRADIANT MODE ***\n");
            double growth_rate = estimate_superradiance_rate(qnm, M, a, m);
            printf("  Growth rate: %.6e\n", growth_rate);
            printf("  Growth timescale: %.6e\n", 1.0/growth_rate);
        } else {
            printf("\n  Non-superradiant mode\n");
        }
    } else {
        printf("ERROR: QNM calculation did not converge!\n");
        return 1;
    }
    
    // Test multiple spins
    printf("\n\nSpin scan for μM = %.3f:\n", mu*M);
    printf("a/M\t\tω_R\t\tω_I\t\tSuperradiant?\n");
    printf("---\t\t---\t\t---\t\t-------------\n");
    
    for (double a_test = 0.0; a_test <= 0.99; a_test += 0.1) {
        QNMResult qnm_test = calculate_qnm(M, a_test, mu, l, m, n);
        if (qnm_test.converged) {
            double omega_h = calculate_omega_horizon(M, a_test);
            int is_super = check_superradiance(qnm_test.omega_real, m, omega_h);
            printf("%.2f\t\t%.6f\t%.6f\t%s\n", 
                   a_test, qnm_test.omega_real, qnm_test.omega_imag,
                   is_super ? "YES" : "NO");
        }
    }
    
    return 0;
} 