# Unified Black Hole Evolution Configuration File
# ===============================================

# Evolution Time Parameters
# -------------------------
t_initial = 0.0                    # Initial time (seconds)
t_final = 3.15e15                  # Final time (seconds) ~ 100 million years
dt_initial = 3.15e10               # Initial timestep (seconds) ~ 1000 years
dt_min = 3.15e7                    # Minimum timestep (seconds) ~ 1 year
dt_max = 3.15e12                   # Maximum timestep (seconds) ~ 100,000 years
tolerance = 1e-6                   # Numerical tolerance

# Physics Parameters
# ------------------
enable_hawking = 1                 # Enable Hawking radiation (1=yes, 0=no)
enable_superradiance = 1           # Enable superradiance (1=yes, 0=no)

# Scalar field mass in eV (will be converted to kg internally)
scalar_mass = 1e-12                # Ultra-light scalar field mass (eV)

# Quantum numbers for superradiance calculation
l_max = 3                          # Maximum orbital quantum number
m_max = 3                          # Maximum azimuthal quantum number  
n_max = 0                          # Maximum overtone number (0=fundamental only)

# Initial scalar field cloud mass as fraction of BH mass
initial_cloud_fraction = 1e-6      # Initial cloud mass / BH mass

# Numerical Parameters
# --------------------
adaptive_timestep = 1              # Use adaptive timestep (1=yes, 0=no)
max_iterations = 1000000           # Maximum number of evolution steps
convergence_threshold = 1e-10      # Convergence criterion
integration_method = 1             # Integration method (0=Euler, 1=RK4, 2=adaptive)

# Output Parameters
# -----------------
output_dir = ./results             # Output directory
output_frequency = 100             # Output every N steps
verbose = 1                        # Verbosity level (0=quiet, 1=normal, 2=verbose, 3=debug)
save_intermediate = 1              # Save intermediate states (1=yes, 0=no)

# Cache Parameters
# ----------------
enable_cache = 1                   # Enable QNM result caching (1=yes, 0=no)
cache_size = 10000                 # Maximum number of cached QNM results
cache_file = ./qnm_cache.dat       # Cache file path

# Example configurations for different scenarios:
# ===============================================

# High-mass black hole (Hawking radiation dominated):
# M ~ 1e31 kg (5000 solar masses), a ~ 0.1
# Hawking timescale ~ 1e67 years (much longer than universe age)
# Superradiance may still be important for light scalars

# Intermediate-mass black hole (competitive regime):
# M ~ 1e30 kg (500 solar masses), a ~ 0.7
# Both effects may be important

# Low-mass black hole (depends on formation mechanism):
# M ~ 1e20 kg (asteroid mass), a ~ 0.9
# Hawking radiation dominates, but superradiance can be fast initially

# Ultra-light scalar field masses of interest:
# - Dark photon: ~ 1e-12 eV
# - Axion-like particles: ~ 1e-22 to 1e-10 eV
# - Fuzzy dark matter: ~ 1e-22 eV

# Notes:
# ------
# 1. Time units are in seconds. For reference:
#    - 1 year ≈ 3.15e7 seconds
#    - 1 million years ≈ 3.15e13 seconds
#    - Age of universe ≈ 4.3e17 seconds

# 2. Mass units are in kg. For reference:
#    - Solar mass ≈ 1.989e30 kg
#    - Planck mass ≈ 2.176e-8 kg

# 3. The scalar field mass is specified in eV and automatically
#    converted to kg using the conversion factor 1 eV = 1.783e-27 kg

# 4. For superradiance to occur, need:
#    - Rotating black hole (a > 0)
#    - Massive scalar field (mu > 0)  
#    - Superradiance condition: omega < m * Omega_H
#    where Omega_H is the horizon angular velocity

# 5. Typical superradiance timescales scale as:
#    t_SR ~ 1/(mu * alpha^7) where alpha = G*M*mu/(hbar*c)
#    For mu ~ 1e-12 eV and M ~ 10 solar masses: t_SR ~ years to millennia

# 6. Hawking radiation timescales scale as:
#    t_H ~ M^3/M_Planck^4 ~ (M/M_sun)^3 * 1e67 years
#    Only becomes important for very small black holes
